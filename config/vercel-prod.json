{"buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm ci", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Environment", "value": "production"}]}], "env": {"VITE_SUPABASE_URL": "https://xxlpybhprndrdlvbuqoj.supabase.co", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh4bHB5Ymhwcm5kcmRsdmJ1cW9qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MzMzNjAsImV4cCI6MjA2NDMwOTM2MH0.H5z2IofSsLGxFX2q3sQle3B9NMDuNTAVj5tOTjKTrHM", "VITE_DEBUG_MODE": "false", "VITE_LOG_LEVEL": "error", "VITE_ENABLE_ANALYTICS": "true", "VITE_ENABLE_ERROR_REPORTING": "true"}}