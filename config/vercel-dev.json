{"buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm ci", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Environment", "value": "development"}]}], "env": {"VITE_SUPABASE_URL": "https://wztqkxsgrwgzeewxcidd.supabase.co", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6dHFreHNncndnemVld3hjaWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0NzAyNjgsImV4cCI6MjA2NzA0NjI2OH0.KyWVK1J8dQObVByDiBbZMtnI__QRRQAnA4-weBotPcM", "VITE_DEBUG_MODE": "true", "VITE_LOG_LEVEL": "debug", "VITE_ENABLE_ANALYTICS": "false", "VITE_ENABLE_ERROR_REPORTING": "false"}}