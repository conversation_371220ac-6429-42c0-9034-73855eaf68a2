import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  // Set project root to parent directory since config is in subdirectory
  root: path.resolve(__dirname, '..'),
  
  // Use config files from config directory
  css: {
    postcss: './config/postcss.config.js',
  },
  
  // Environment configuration
  envDir: './config',
  envPrefix: ['VITE_'],
  
  // Server configuration
  server: {
    host: "::",
    port: 8080,
    // Allow ngrok and other external hosts
    allowedHosts: [
      '.ngrok.io',
      '.ngrok-free.app',
      'localhost',
      '127.0.0.1'
    ],
  },
  
  // Global definitions
  define: {
    global: 'globalThis',
  },
  
  // Plugins
  plugins: [
    react(),
  ],
  
  // Path aliases
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "../src"),
      "@/shared": path.resolve(__dirname, "../src/shared"),
      "@/app": path.resolve(__dirname, "../src/app"),
      "@/pages": path.resolve(__dirname, "../src/pages"),
      "@/integrations": path.resolve(__dirname, "../src/integrations"),
    },
  },
  
  // Build configuration
  build: {
    target: 'esnext',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        // Basic vendor chunk splitting for better caching
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['@radix-ui/react-slot', '@radix-ui/react-toast', '@radix-ui/react-tooltip'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'query-vendor': ['@tanstack/react-query'],
        },
      },
    },
  },
});