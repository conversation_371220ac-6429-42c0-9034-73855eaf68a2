# OpenRouter LLM Response Streaming for Structured Outputs

This document summarizes how to enable and handle LLM response streaming for OpenRouter structured outputs based on the latest OpenRouter API documentation.

---

## Enabling Streaming for Chat Completions

To enable streaming responses from the OpenRouter API's chat completions endpoint, set the `stream` parameter to `true` in the request payload. The API will then send Server-Sent Events (SSE) chunks that can be processed incrementally.

### Example (Python)

```python
import requests
import json

question = "How would you build the tallest building ever?"

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
  "Authorization": f"Bearer {{API_KEY_REF}}",
  "Content-Type": "application/json"
}

payload = {
  "model": "{{MODEL}}",
  "messages": [{"role": "user", "content": question}],
  "stream": True
}

buffer = ""
with requests.post(url, headers=headers, json=payload, stream=True) as r:
  for chunk in r.iter_content(chunk_size=1024, decode_unicode=True):
    buffer += chunk
    while True:
      try:
        line_end = buffer.find('\n')
        if line_end == -1:
          break

        line = buffer[:line_end].strip()
        buffer = buffer[line_end + 1:]

        if line.startswith('data: '):
          data = line[6:]
          if data == '[DONE]':
            break

          try:
            data_obj = json.loads(data)
            content = data_obj["choices"][0]["delta"].get("content")
            if content:
              print(content, end="", flush=True)
          except json.JSONDecodeError:
            pass
      except Exception:
        break
```

### Example (TypeScript)

```typescript
const question = 'How would you build the tallest building ever?';
const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    Authorization: `Bearer ${API_KEY_REF}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: '{{MODEL}}',
    messages: [{ role: 'user', content: question }],
    stream: true,
  }),
});

const reader = response.body?.getReader();
if (!reader) {
  throw new Error('Response body is not readable');
}

const decoder = new TextDecoder();
let buffer = '';

try {
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });

    while (true) {
      const lineEnd = buffer.indexOf('\n');
      if (lineEnd === -1) break;

      const line = buffer.slice(0, lineEnd).trim();
      buffer = buffer.slice(lineEnd + 1);

      if (line.startsWith('data: ')) {
        const data = line.slice(6);
        if (data === '[DONE]') break;

        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices[0].delta.content;
          if (content) {
            console.log(content);
          }
        } catch (e) {
          // Ignore invalid JSON
        }
      }
    }
  }
} finally {
  reader.cancel();
}
```

---

## Streaming Structured JSON Outputs

To stream structured outputs (e.g., JSON schema-based responses), include the `response_format` parameter with the type set to `"json_schema"` and set `"stream": true` in the request body. This instructs the model to stream partial, valid JSON chunks that conform to the defined schema.

### Minimal JSON Payload Example

```json
{
  "stream": true,
  "response_format": {
    "type": "json_schema",
    // ... your JSON schema here
  }
}
```

---

## Cancelling Streaming Requests

OpenRouter supports cancellation of streaming requests to immediately stop the stream and halt billing.

### Python Example (Using threading.Event)

```python
import requests
from threading import Event, Thread

def stream_with_cancellation(prompt: str, cancel_event: Event):
    with requests.Session() as session:
        response = session.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers={"Authorization": f"Bearer {{API_KEY_REF}}"},
            json={"model": "{{MODEL}}", "messages": [{"role": "user", "content": prompt}], "stream": True},
            stream=True
        )

        try:
            for line in response.iter_lines():
                if cancel_event.is_set():
                    response.close()
                    return
                if line:
                    print(line.decode(), end="", flush=True)
        finally:
            response.close()

cancel_event = Event()
stream_thread = Thread(target=lambda: stream_with_cancellation("Write a story", cancel_event))
stream_thread.start()

# To cancel the stream:
cancel_event.set()
```

### TypeScript Example (Using AbortController)

```typescript
const controller = new AbortController();

try {
  const response = await fetch(
    'https://openrouter.ai/api/v1/chat/completions',
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${API_KEY_REF}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: '{{MODEL}}',
        messages: [{ role: 'user', content: 'Write a story' }],
        stream: true,
      }),
      signal: controller.signal,
    },
  );

  // Process the stream...
} catch (error) {
  if (error.name === 'AbortError') {
    console.log('Stream cancelled');
  } else {
    throw error;
  }
}

// To cancel the stream:
controller.abort();
```

---

## Additional Notes

- OpenRouter sends occasional SSE comment lines starting with a colon (`:`) to keep connections alive; these can be safely ignored.
- Usage data can be included in streaming responses by setting `usage: { include: true }` in the request.
- The OpenRouter Vercel AI SDK supports streaming text generation with a `streamText` function.
- Reasoning tokens can be streamed separately by setting `reasoning.max_tokens` in the request.

---

This summary provides the essential code snippets and configuration options to enable and handle streaming LLM responses with OpenRouter, including structured JSON outputs and stream cancellation.

For full details, see the official OpenRouter documentation at https://openrouter.ai/docs/api-reference/streaming.mdx and https://openrouter.ai/docs/features/structured-outputs.mdx