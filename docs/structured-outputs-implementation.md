# Structured Outputs Implementation Guide

## Overview

This document describes the implementation of OpenAI's Structured Outputs feature across the SalahScribe translation and transliteration services. The feature ensures that LLM responses always conform to a specified JSON Schema, eliminating the need for parsing JSON from markdown code blocks.

## Key Benefits

1. **Guaranteed JSON Format**: Responses are always valid JSON matching the defined schema
2. **No Parsing Required**: Eliminates the need to extract JSON from markdown code blocks
3. **Type Safety**: Responses are validated against the schema
4. **Consistency**: All endpoints use the same structured approach

## Implementation Details

### 1. Schema Definitions

All response schemas are defined in `supabase/functions/_shared/schemas.ts`:

```typescript
// Translation Response Schema
export const TranslationResponseSchema = {
  type: 'object',
  properties: {
    translation: {
      type: 'string',
      description: 'The translated text'
    }
  },
  required: ['translation'],
  additionalProperties: false
} as const;

// Transliteration Response Schema
export const TransliterationResponseSchema = {
  type: 'object',
  properties: {
    transliteration: {
      type: 'string',
      description: 'The transliterated text'
    }
  },
  required: ['transliteration'],
  additionalProperties: false
} as const;

// Combined Response Schema (for unified endpoints)
export const CombinedResponseSchema = {
  type: 'object',
  properties: {
    transliteration: {
      type: 'string',
      description: 'The transliterated text'
    },
    translation: {
      type: 'string',
      description: 'The translated text'
    }
  },
  required: ['transliteration', 'translation'],
  additionalProperties: false
} as const;

// OCR Response Schema (for image processing)
export const OCRResponseSchema = {
  type: 'object',
  properties: {
    extractedText: {
      type: 'string',
      description: 'The text extracted from the image'
    }
  },
  required: ['extractedText'],
  additionalProperties: false
} as const;
```

### 2. API Call Format

When calling the OpenAI API, include the `responseFormat` parameter:

```typescript
const response = await provider.callAPI({
  prompt: promptText,
  responseFormat: {
    type: "json_schema",
    json_schema: {
      name: "response_name",
      schema: ResponseSchema,
      strict: true
    }
  }
});
```

### 3. Updated Prompts

All prompts have been simplified to remove JSON formatting instructions:

**Before:**
```typescript
return `Your task is to translate...

Return the result as JSON in this exact format:
{
  "translation": "the translated text"
}

Input text: "${text}"`;
```

**After:**
```typescript
return `Your task is to translate...

Input text: "${text}"`;
```

### 4. Simplified Response Parsing

Response parsers now expect pure JSON without markdown wrapping:

**Before:**
```typescript
function parseResponse(response: string): string | null {
  // Extract JSON from markdown code blocks
  const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
  if (jsonMatch) {
    const json = JSON.parse(jsonMatch[1]);
    return json.translation;
  }
  // Fallback parsing logic...
}
```

**After:**
```typescript
function parseResponse(response: string): string | null {
  const result = JSON.parse(response);
  return result.translation || null;
}
```

## Affected Endpoints

### 1. `/translate`
- **Schema**: `TranslationResponseSchema`
- **Status**: ✅ Implemented

### 2. `/transliterate`
- **Schema**: `TransliterationResponseSchema`
- **Status**: ✅ Implemented

### 3. `/transliterate-image`
- **OCR Schema**: `OCRResponseSchema`
- **Transform Schema**: `CombinedResponseSchema`
- **Status**: ✅ Implemented

### 4. `/transliterate-audio`
- **Status**: ⏳ Pending (endpoint not yet implemented)

## Provider Support

### OpenAI Provider
- **Status**: ✅ Full support
- **Models**: All GPT models support structured outputs
- **Implementation**: Complete for both text and image processing

### Gemini Provider
- **Status**: ❌ Not supported
- **Fallback**: Uses traditional JSON parsing

### OpenRouter Provider
- **Status**: ❌ Not supported
- **Fallback**: Uses traditional JSON parsing

## Testing

Use the provided test script to verify structured outputs:

```bash
# Set your OpenAI API key
export OPENAI_API_KEY="your-api-key"

# Run the test script
deno run --allow-net --allow-env test-structured-outputs.ts
```

The test script verifies that:
1. Responses are pure JSON (no markdown wrapping)
2. Response structure matches expected schema
3. All endpoints work correctly with structured outputs

## Migration Notes

### For Developers
1. When using OpenAI provider, responses will always be valid JSON
2. No need to handle markdown-wrapped JSON responses
3. Response parsing is simplified and more reliable

### For API Consumers
1. Response format remains the same
2. More consistent and reliable responses
3. Faster response parsing

## Future Enhancements

1. **Add support for other providers** when they implement structured outputs
2. **Extend to streaming responses** when OpenAI adds support
3. **Add more complex schemas** for advanced use cases
4. **Implement retry logic** for schema validation failures

## Troubleshooting

### Issue: Response not matching schema
**Solution**: Check that the prompt is clear and the schema accurately represents the expected output

### Issue: Provider not supporting structured outputs
**Solution**: The system automatically falls back to traditional parsing for unsupported providers

### Issue: Image processing failing
**Solution**: Ensure the image endpoint is using the correct OCR schema for text extraction

## Conclusion

The structured outputs implementation provides a more robust and reliable way to handle LLM responses. By ensuring responses always conform to a defined schema, we eliminate parsing errors and improve the overall reliability of the translation and transliteration services.