# OCR Structured Output Fix Documentation

## Problem Description

The image OCR service was failing with the error:
```
Failed to parse OCR response: SyntaxError: Unexpected token 'ب', "بِسْمِ الل"... is not valid JSON
```

The OCR was returning plain text instead of the expected JSON format:
- **Actual Response**: `بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ...`
- **Expected Response**: `{"extractedText": "بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ..."}`

## Root Cause

Some image models (particularly when using OpenRouter with models like `google/gemini-2.0-flash-exp:free`) don't fully support structured outputs for image processing. They return plain text even when a JSON schema is requested.

## Solution

I've implemented a fallback mechanism in the LLM providers that automatically wraps plain text responses in the expected JSON format when:
1. Structured output is requested (responseFormat is provided)
2. The response is plain text (doesn't start with '{')
3. The schema name is 'ocr_result'

### Code Changes

In `supabase/functions/_shared/llm-providers.ts`:

#### OpenRouterProvider.callAPIWithImage:
```typescript
const content = responseData.choices[0].message.content;

// If we requested structured output but got plain text, wrap it
if (request.responseFormat && typeof content === 'string' && !content.trim().startsWith('{')) {
  // For OCR responses, wrap plain text in expected format
  if (request.responseFormat.json_schema.name === 'ocr_result') {
    return JSON.stringify({ extractedText: content });
  }
}

return content;
```

#### OpenAIProvider.callAPIWithImage:
```typescript
const text = choice.message.content;
if (!text) {
  throw new Error('Failed to extract text from OpenAI response');
}

// If we requested structured output but got plain text, wrap it
if (request.responseFormat && typeof text === 'string' && !text.trim().startsWith('{')) {
  // For OCR responses, wrap plain text in expected format
  if (request.responseFormat.json_schema.name === 'ocr_result') {
    return JSON.stringify({ extractedText: text });
  }
}

return text;
```

## How It Works

1. **Detection**: The code checks if the response is plain text when JSON was expected
2. **Wrapping**: If it's an OCR response (schema name is 'ocr_result'), it wraps the text in the expected format
3. **Parsing**: The wrapped response can now be parsed successfully in the endpoint

## Benefits

- **Backward Compatible**: Works with both models that support structured outputs and those that don't
- **Automatic**: No changes needed in the endpoint code
- **Graceful Fallback**: Handles edge cases without failing
- **Consistent Interface**: The endpoint always receives the expected format

## Testing

To test the fix:
1. Upload an image with Arabic text
2. The OCR should successfully extract the text
3. The response should include `extractedText`, `transliteration`, and `translation` fields

## Future Improvements

1. **Model Detection**: Automatically detect which models support structured outputs
2. **Provider-Specific Logic**: Add more sophisticated handling for different providers
3. **Schema Validation**: Add validation to ensure wrapped responses match the schema
4. **Logging**: Add debug logging to track when wrapping occurs

## Conclusion

This fix ensures that the image OCR service works reliably regardless of whether the underlying model supports structured outputs for images. The automatic wrapping provides a seamless experience while maintaining the expected API contract.