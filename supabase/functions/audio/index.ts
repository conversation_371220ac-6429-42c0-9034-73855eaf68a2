import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { corsHeaders } from '../_shared/cors.ts';

interface AudioRequest {
  text: string;
  lang: 'ar' | 'en';
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { text, lang }: AudioRequest = await req.json();

    if (!text || !lang) {
      throw new Error('Missing required parameters: text and lang');
    }

    // Get the OpenAI API key and model from environment
    const OPENAI_API_KEY = Deno.env.get('AUDIO_API_KEY');
    const AUDIO_MODEL = Deno.env.get('AUDIO_MODEL') || 'gpt-4o-mini-tts';
    const ARABIC_VOICE = Deno.env.get('AUDIO_VOICE_ARABIC') || 'onyx';
    const ENGLISH_VOICE = Deno.env.get('AUDIO_VOICE_ENGLISH') || 'onyx';
    const INSTRUCTIONS_ARABIC = Deno.env.get('AUDIO_INSTRUCTIONS_ARABIC') || '';
    const INSTRUCTIONS_ENGLISH = Deno.env.get('AUDIO_INSTRUCTIONS_ENGLISH') || '';
    
    if (!OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    // Determine voice based on language
    const voice = lang === 'ar' ? ARABIC_VOICE : ENGLISH_VOICE;
    
    // Get instructions from environment variables based on language
    const finalInstructions = lang === 'ar' ? INSTRUCTIONS_ARABIC : INSTRUCTIONS_ENGLISH;
    
    // Use the v1/audio/speech endpoint for audio generation
    const requestBody: any = {
      model: AUDIO_MODEL,
      input: text,
      voice: voice.toLowerCase(), // TTS endpoint expects lowercase voice names
      response_format: 'mp3',
    };
    
    // Add instructions if available from environment variables
    if (finalInstructions) {
      requestBody.instructions = finalInstructions;
    }
    
    const response = await fetch('https://api.openai.com/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`OpenAI TTS API error: ${error}`);
    }

    // Get the audio data directly as ArrayBuffer
    const buffer = await response.arrayBuffer();
    const audioData = new Uint8Array(buffer);

    // Return the audio data with appropriate headers
    return new Response(audioData, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'audio/mp3',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Audio generation error:', error);
    
    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to generate audio',
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
  }
});