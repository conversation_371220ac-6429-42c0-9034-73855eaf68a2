import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { getEnvConfig } from "../_shared/env-config.ts";
import { createTextProvider, LLMProvider } from "../_shared/text-providers.ts";
import { createTransliterationPrompt, parseTransliterationResponse } from "../_shared/transliteration-prompt.ts";
import { TransliterationResponseSchema } from "../_shared/schemas.ts";
import { validateLanguage, validateText, validateRequestFields, type LanguageCode } from "../_shared/validation.ts";
import { createErrorResponse, createSuccessResponse, ApiError, withErrorHandling } from "../_shared/error-handling.ts";

import { corsHeaders as CORS_HEADERS } from "../_shared/cors.ts";

// Types
interface TransliterationRequest {
  text: string;
  sourceLang: LanguageCode;
  stream?: boolean;
}

interface TransliterationResponse {
  transliteration: string;
  original: string;
}


// SSE helper for streaming
function createSSEResponse(stream: ReadableStream): Response {
  return new Response(stream, {
    headers: {
      ...CORS_HEADERS,
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}

// Main handler
const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      status: 200,
      headers: CORS_HEADERS 
    });
  }

  // Parse and validate request
  const requestData = await req.json();
  
  if (!validateRequestFields<TransliterationRequest>(requestData, ['text', 'sourceLang'])) {
    throw new ApiError('Missing required fields: text (string), sourceLang (ar|en)', 400);
  }

  const { text, sourceLang, stream } = requestData;
  
  // Validate text
  const textValidation = validateText(text);
  if (!textValidation.valid) {
    throw new ApiError(textValidation.error!, 400);
  }
  
  // Validate language
  if (!validateLanguage(sourceLang)) {
    throw new ApiError('Invalid language code. Use "ar" for Arabic or "en" for English.', 400);
  }
    const useStreaming = stream ?? false;

    // Get configuration
    let config;
    try {
      config = getEnvConfig();
    } catch (error) {
      console.error('Environment configuration error:', error);
      throw new ApiError('Server configuration error', 500, 'config_error');
    }

    // Log request info
    // Create LLM provider instance
    let provider: LLMProvider;
    try {
      provider = createTextProvider(config);
    } catch (error) {
      console.error('Failed to create LLM provider:', error);
      throw new ApiError('Failed to initialize LLM provider', 500, 'provider_error');
    }

    console.log('Processing transliteration request, text length:', text.length, 'streaming:', useStreaming);

    // Create prompt and call API
    const prompt = createTransliterationPrompt(text, sourceLang);
    
    const responseFormat = {
      type: "json_schema" as const,
      json_schema: {
        name: "transliteration_result",
        schema: TransliterationResponseSchema,
        strict: true,
      },
    };

    if (useStreaming) {
      const stream = new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          
          try {
            const eventGenerator = provider.streamStructured({
              prompt,
              responseFormat,
            });

            for await (const event of eventGenerator) {
              const chunk = `data: ${JSON.stringify({ delta: event })}\n\n`;
              controller.enqueue(encoder.encode(chunk));
            }

            // Send final DONE message
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));

          } catch (error) {
            console.error('Streaming error:', error);
            const errorChunk = `data: ${JSON.stringify({ error: { message: error.message, code: 'STREAM_ERROR' } })}\n\n`;
            controller.enqueue(encoder.encode(errorChunk));
          } finally {
            controller.close();
          }
        },
      });

      return createSSEResponse(stream);

    } else {
      // Non-streaming response with structured output
      const response = await provider.callAPI({
        prompt,
        responseFormat,
      });
      const llmResponse = await provider.parseResponse(response);
      
      // Parse the transliteration response
      const transliteration = parseTransliterationResponse(llmResponse.text);
      
      if (!transliteration) {
        console.error('Empty response returned');
        throw new ApiError('Failed to generate transliteration', 500, 'empty_response');
      }

      // Return success response
      return createSuccessResponse({
        transliteration,
        original: text
      });
    }
      
};

// Serve with error handling
serve(withErrorHandling(handler, 'transliterate'));
