/**
 * Creates a prompt for bidirectional text transformation (transliteration + translation)
 * @param text The input text
 * @param sourceLang Source language ('ar' or 'en')
 * @param targetLang Target language ('ar' or 'en')
 * @returns A formatted prompt for the LLM
 */
export function createUnifiedPrompt(
  text: string,
  sourceLang: 'ar' | 'en',
  targetLang: 'ar' | 'en'
): string {
  if (sourceLang === 'ar' && targetLang === 'en') {
    // Arabic to English
    return `You are an expert Arabic translator specializing in Islamic texts, du'ās, and Quranic verses.

Your task is to:
1. Transliterate the Arabic text into Latin characters using a phoneme-for-phoneme approach
2. Translate the Arabic text into English

For transliteration, follow these key rules:
- ا = ā (long a)
- ع = ʿ (ayn)
- ء = ' (hamza)
- Use standard academic transliteration conventions

Input text:
"${text}"`;
  } else if (sourceLang === 'en' && targetLang === 'ar') {
    // English to Arabic
    return `You are an expert translator specializing in Islamic and Arabic texts.

Your task is to:
1. Translate the English text into Arabic
2. Provide a transliteration of the Arabic translation using Latin characters

For transliteration, follow these key rules:
- ا = ā (long a)
- ع = ʿ (ayn)
- ء = ' (hamza)
- Use standard academic transliteration conventions

Input text:
"${text}"`;
  } else {
    throw new Error(`Unsupported language pair: ${sourceLang} to ${targetLang}`);
  }
}

/**
 * Parses the LLM response to extract transliteration and translation
 * @param response The raw LLM response (should be pure JSON with structured outputs)
 * @returns Parsed transliteration and translation
 */
export function parseUnifiedResponse(response: string): {
  transliteration: string;
  translation: string;
} {
  try {
    const parsed = JSON.parse(response.trim());
    
    if (!parsed.transliteration || !parsed.translation) {
      throw new Error('Response missing required fields');
    }
    
    return {
      transliteration: parsed.transliteration,
      translation: parsed.translation,
    };
  } catch (error) {
    console.error('Failed to parse unified response:', error, 'Response:', response);
    throw new Error('Invalid unified response format');
  }
}