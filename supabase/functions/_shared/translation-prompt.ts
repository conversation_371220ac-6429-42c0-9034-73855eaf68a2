import { parseTranslationResponse } from './response-parser.ts';

/**
 * Creates a prompt for translation ONLY (no transliteration)
 * @param text The input text
 * @param sourceLang Source language ('ar' or 'en')
 * @param targetLang Target language ('ar' or 'en')
 * @returns A formatted prompt for the LLM
 */
export function createTranslationPrompt(
  text: string,
  sourceLang: 'ar' | 'en',
  targetLang: 'ar' | 'en'
): string {
  if (sourceLang === 'ar' && targetLang === 'en') {
    // Arabic to English translation
    return `You are an expert Arabic translator specializing in Islamic texts, du'ās, and Quranic verses.

Your task is to translate the following Arabic text into English. Provide ONLY the translation, NOT transliteration.

Guidelines:
- Provide accurate, contextual translation
- Preserve the meaning and nuance of Islamic terminology
- Use commonly accepted English translations for Islamic terms
- Maintain the tone and style of the original text

Input text:
"${text}"`;
  } else if (sourceLang === 'en' && targetLang === 'ar') {
    // English to Arabic translation
    return `You are an expert translator specializing in Islamic and Arabic texts.

Your task is to translate the following English text into Arabic. Provide ONLY the translation, NOT transliteration.

Guidelines:
- Provide accurate, natural Arabic translation
- Use appropriate Arabic grammar and vocabulary
- Maintain proper Islamic terminology when applicable
- Preserve the tone and meaning of the original text

Input text:
"${text}"`;
  } else {
    throw new Error(`Unsupported language pair: ${sourceLang} to ${targetLang}`);
  }
}

// Re-export the parser for backward compatibility
export { parseTranslationResponse };