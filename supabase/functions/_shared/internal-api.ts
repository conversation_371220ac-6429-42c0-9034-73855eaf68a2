/**
 * Internal API helper for making calls between edge functions
 * This enables better code reuse by allowing functions to call each other
 *
 * In Supabase Edge Functions, we can directly import and call the handler functions
 * instead of making HTTP requests, which is more efficient and avoids network overhead.
 */

import { createTextProvider } from "./text-providers.ts";
import { createTranslationPrompt, parseTranslationResponse } from "./translation-prompt.ts";
import { createTransliterationPrompt, parseTransliterationResponse } from "./transliteration-prompt.ts";
import { TranslationResponseSchema, TransliterationResponseSchema } from "./schemas.ts";
import { getEnvConfig } from "./env-config.ts";
import { ApiError } from "./error-handling.ts";
import type { LanguageCode } from "./validation.ts";

export class InternalApi {
  /**
   * Call the translate functionality directly
   */
  async translate(text: string, sourceLang: LanguageCode, targetLang: LanguageCode): Promise<string> {
    const config = getEnvConfig();
    const provider = createTextProvider(config);
    const prompt = createTranslationPrompt(text, sourceLang, targetLang);
    
    const response = await provider.callAPI({
      prompt,
      responseFormat: {
        type: "json_schema",
        json_schema: {
          name: "translation_result",
          schema: TranslationResponseSchema,
          strict: true
        }
      }
    });
    
    const llmResponse = await provider.parseResponse(response);
    const translation = parseTranslationResponse(llmResponse.text);
    
    if (!translation) {
      throw new ApiError('Failed to generate translation', 500, 'empty_response');
    }
    
    return translation;
  }

  /**
   * Call the transliterate functionality directly
   */
  async transliterate(text: string, sourceLang: LanguageCode): Promise<string> {
    const config = getEnvConfig();
    const provider = createTextProvider(config);
    const prompt = createTransliterationPrompt(text, sourceLang);
    
    const response = await provider.callAPI({
      prompt,
      responseFormat: {
        type: "json_schema",
        json_schema: {
          name: "transliteration_result",
          schema: TransliterationResponseSchema,
          strict: true
        }
      }
    });
    
    const llmResponse = await provider.parseResponse(response);
    const transliteration = parseTransliterationResponse(llmResponse.text);
    
    if (!transliteration) {
      throw new ApiError('Failed to generate transliteration', 500, 'empty_response');
    }
    
    return transliteration;
  }

  /**
   * Call the transliterate functionality with streaming
   */
  async *streamTransliterate(text: string, sourceLang: LanguageCode): AsyncGenerator<any> {
    const config = getEnvConfig();
    const provider = createTextProvider(config);
    const prompt = createTransliterationPrompt(text, sourceLang);
    
    const responseFormat = {
      type: "json_schema" as const,
      json_schema: {
        name: "transliteration_result",
        schema: TransliterationResponseSchema,
        strict: true,
      },
    };

    yield* provider.streamStructured({
      prompt,
      responseFormat,
    });
  }

  /**
   * Call the translate functionality with streaming
   */
  async *streamTranslate(text: string, sourceLang: LanguageCode, targetLang: LanguageCode): AsyncGenerator<any> {
    const config = getEnvConfig();
    const provider = createTextProvider(config);
    const prompt = createTranslationPrompt(text, sourceLang, targetLang);

    const responseFormat = {
      type: "json_schema" as const,
      json_schema: {
        name: "translation_result",
        schema: TranslationResponseSchema,
        strict: true,
      },
    };

    yield* provider.streamStructured({
      prompt,
      responseFormat,
    });
  }

  /**
   * Call both translate and transliterate functionality in parallel
   */
  async translateAndTransliterate(
    text: string,
    sourceLang: LanguageCode,
    targetLang: LanguageCode
  ): Promise<{ translation: string; transliteration: string }> {
    const [translation, transliteration] = await Promise.all([
      this.translate(text, sourceLang, targetLang),
      this.transliterate(text, sourceLang)
    ]);

    return { translation, transliteration };
  }
}

/**
 * Create an internal API client
 * Since we're calling functions directly, no configuration is needed
 */
export function createInternalApiClient(): InternalApi {
  return new InternalApi();
}