/**
 * Utilities for parsing LLM responses with structured outputs
 */

/**
 * Safely parse JSON with fallback
 * @param text The text to parse (should be pure JSON with structured outputs)
 * @param fallback Optional fallback value if parsing fails
 * @returns Parsed object or fallback/null
 */
export function safeJSONParse<T>(text: string, fallback?: T): T | null {
  try {
    const trimmed = text.trim();
    
    // First try direct parsing (most common case)
    if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
      return JSON.parse(trimmed);
    }
    
    // Handle cases where LLM adds extra content after JSON
    // This regex finds the first complete JSON object in the text
    const jsonMatch = trimmed.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/);
    if (jsonMatch) {
      try {
        const extracted = jsonMatch[0];
        const parsed = JSON.parse(extracted);
        console.warn('Extracted JSON from mixed content. LLM response contained non-JSON text.');
        return parsed;
      } catch (e) {
        // If extraction failed, fall through to try full parse
      }
    }
    
    // Last resort: try parsing the whole thing
    return JSON.parse(trimmed);
  } catch (error) {
    console.error('Failed to parse JSON from structured output:', error, 'Text snippet:', text.substring(0, 200));
    return fallback ?? null;
  }
}

/**
 * Parse a transliteration response from structured output
 * @param response The raw LLM response (should be pure JSON)
 * @returns The transliterated text
 */
export function parseTransliterationResponse(response: string): string {
  const parsed = safeJSONParse<{ transliteration: string }>(response);
  
  if (parsed?.transliteration && typeof parsed.transliteration === 'string') {
    return parsed.transliteration;
  }
  
  throw new Error('Invalid transliteration response format');
}

/**
 * Parse a translation response from structured output
 * @param response The raw LLM response (should be pure JSON)
 * @returns The translated text
 */
export function parseTranslationResponse(response: string): string {
  const parsed = safeJSONParse<{ translation: string }>(response);
  
  if (parsed?.translation && typeof parsed.translation === 'string') {
    return parsed.translation;
  }
  
  throw new Error('Invalid translation response format');
}

/**
 * Parse a combined transform response (transliteration + translation) from structured output
 * @param response The raw LLM response (should be pure JSON)
 * @returns Object with transliteration and translation
 */
export function parseTransformResponse(response: string): { transliteration: string; translation: string } {
  const parsed = safeJSONParse<{ transliteration: string; translation: string }>(response);
  
  if (parsed?.transliteration && parsed?.translation) {
    return {
      transliteration: parsed.transliteration,
      translation: parsed.translation
    };
  }
  
  throw new Error('Invalid transform response format');
}