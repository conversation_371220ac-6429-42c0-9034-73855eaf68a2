import { parseTransliterationResponse } from './response-parser.ts';

/**
 * Creates a prompt for transliteration ONLY (no translation)
 * @param text The input text
 * @param sourceLang Source language ('ar' or 'en')
 * @returns A formatted prompt for the LLM
 */
export function createTransliterationPrompt(
  text: string,
  sourceLang: 'ar' | 'en'
): string {
  if (sourceLang === 'ar') {
    // Prompt 1 - Arabic to English transliteration
    return `You are an expert Arabic transliterator specializing in Islamic texts, du'ās, and Quranic verses. Your task is to convert Arabic text into accurate English transliteration using the following transliteration scheme. Note that it should be a phoneme-for-phoneme transliteration, such that saying the letters produces the exact same sound as the arabic. This is uncommon in arabic transliteration, so pay extra attention and make sure you follow this scheme:

Transliteration Scheme:
ا = ā
ب = b
ت = t
ث = th
ج = j
ح = ḥ
خ = kh
د = d
ذ = dh
ر = r
ز = z
س = s
ش = sh
ص = ṣ
ض = ḍ
ط = ṭ
ظ = d͟h
ع = ʿ
غ = gh
ف = f
ق = q
ك = k
ل = l
م = m
ن = n
هـ = h
ء = '
و = w
ي = y

Alif after fatḥa = ā
Yā after kasra = ī
Wāw after ḍammah = ū

Where there is a point for natural pause, the transliteration should be adjusted to follow the usual stopping rules in Arabic (waqf), and have a comma to indicate the pause

Merge words together where appropriate to preserve the phonetics of the speech, for example:
Input: 
أَشْهَدُ أَنْ لَا إِلَٰهَ إِلَّا ٱللَّٰهُ وَأَشْهَدُ أَنَّ مُحَمَّدًا رَسُولُ ٱللَّٰهِ
INCORRECT OUTPUT:
Ash-hadu an lā ilāha illā llāhu wa ash-hadu anna Muḥammadan rasūlu llāh
CORRECT OUTPUT:
Ash-hadu allā ilāha illallāh, wa ash-hadu-'anna Muḥammadar-rasūlullāh

Full transliteration examples:

Input:
اقْرَأْ بِاسْمِ رَبِّكَ الَّذِي خَلَقَ
Output:
Iqra' bismi rabbikalladhī khalaq

Input:
خَلَقَ الْإِنسَانَ مِنْ عَلَقٍ
Output:
Khalaqal-'insāna min ʿalaq

Input:
اقْرَأْ وَرَبُّكَ الْأَكْرَمُ
Output:
Iqra' wa rabbukal-'akram

Input:
الَّذِي عَلَّمَ بِالْقَلَمِ
Output:
Alladhī ʿallama bil-qalam

Input:
عَلَّمَ الْإِنسَانَ مَا لَمْ يَعْلَمْ
Output:
ʿAllamal-'insāna mā lam yaʿ-lam

Input:
اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْت، أَعوذُ بِكَ مِنْ شَرِّ مَا صَنَعْت، أَبوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ وَأَبوءُ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتُ
Output:
Allāhumma anta rabbī, lā ilāha illā ant, khalaqtanī wa anā ʿabduk, wa-'anā ʿalā ʿahdika wa waʿdika mastaṭaʿt, aʿūdhu bika min sharri mā ṣanaʿt, abū'u laka biniʿmatika ʿalayy, wa abū'u bi-dhanbī, faghfir lī fa innahu lā yaghfirudh-dhunūba illā ant

"${text}"`;
  } else if (sourceLang === 'en') {
    // Prompt 2 - English to Arabic transliteration
    return `You are an expert transliteration engine. Your sole purpose is to take English text as input and output its phonetic representation using the Arabic script. You do not translate the meaning; you transcribe the sound.

**Core Directives:**

1.  **Transliterate, Do Not Translate:** Your primary function is to represent the *pronunciation* of the English words using Arabic letters and diacritics ('ḥarakāt'). You must ignore the meaning of the words entirely.
2.  **Phonetic Accuracy:** Base your transliteration on standard American or British English pronunciation. Strive for the closest phonetic match possible using Arabic characters.
3.  **Output Format:** Your output must consist *only* of the transliterated Arabic text. Do not include any of the original English, romanization, or explanatory phrases like "Here is the transliteration:".

**Transliteration Rules & Guidelines:**

*   **Vowels:**
    *   Use 'ḥarakāt' (short vowel marks) for short vowel sounds:
        *   'a' as in "cat" -> 'فَتْحَة' ( َ ) on the preceding consonant (e.g., 'cat' -> 'كَات')
        *   'i' as in "sit" -> 'كَسْرَة' ( ِ ) (e.g., 'sit' -> 'سِت')
        *   'u' as in "put" -> 'ضَمَّة' ( ُ ) (e.g., 'put' -> 'بُت')
    *   Use long vowel letters for long vowel sounds:
        *   'ā/aa' as in "car" -> 'ا' (Alif) (e.g., 'car' -> 'كَار')
        *   'ē/ee' as in "see" -> 'ي' (Yā'') (e.g., 'see' -> 'سِي')
        *   'ō/oo' as in "go" or "food" -> 'و' (Wāw) (e.g., 'go' -> 'جُو', 'food' -> 'فُود')
*   **Consonants:**
    *   Use the closest Arabic equivalent for each English consonant sound.
    *   For sounds not native to Arabic:
        *   **P** -> Transliterate as **ب** (Bā'). (e.g., 'party' -> 'بَارْتِي')
        *   **V** -> Transliterate as **ف** (Fā'). (e.g., 'video' -> 'فِيدِيُو')
        *   **G** (as in "go") -> Transliterate as **ج** (Jīm) or **غ** (Ghayn) depending on regional convention, but be consistent. Default to **ج**. (e.g., 'game' -> 'جِيم')
*   **Digraphs:**
    *   **ch** -> Transliterate as **تش** (Tā' + Shīn). (e.g., 'chat' -> 'تْشَات')
    *   **sh** -> Transliterate as **ش** (Shīn). (e.g., 'show' -> 'شُو')
    *   **th** (unvoiced, as in "think") -> **ث** (Thā'). (e.g., 'think' -> 'ثِنْك')
    *   **th** (voiced, as in "the") -> **ذ** (Dhāl). (e.g., 'the' -> 'ذَا')
*   **Silent Letters:** Ignore silent letters. Transliterate only the sounds that are spoken (e.g., 'know' -> 'نُو', 'write' -> 'رَايْت').
*   **Word Separation:** Preserve the separation between words. Transliterate word by word, separated by a space.

**Examples:**

*   **Input:** 'How are you'
*   **Correct Output:** 'هَاو آر يُو'
*   **Incorrect Output:** 'كيف حالك؟' (This is translation)
*   **Incorrect Output:** 'The transliteration is: هَاو آر يُو' (This includes extra text)

*   **Input:** 'My name is John'
*   **Correct Output:** 'مَاي نِيم إِز جُون'

*   **Input:** 'Please check the new ChatGPT version'
*   **Correct Output:** 'بْلِيز تْشِيك ذَا نْيُو تْشَات جِي بِي تِي فِيرْژِن'

Your function is that of a precise, automated tool. Adhere strictly to these rules. Do not engage in conversation or deviate from the transliteration task.

Input text:
"${text}"`;
  } else {
    throw new Error(`Unsupported source language: ${sourceLang}`);
  }
}

// Re-export the parser for backward compatibility
export { parseTransliterationResponse };
