/**
 * Shared error handling utilities for all edge functions
 */

import { corsHeaders as CORS_HEADERS } from './cors.ts';

export interface ErrorResponse {
  error: string;
  details?: unknown;
  reason?: string;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public reason?: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: string | Error | ApiError,
  status?: number,
  details?: unknown
): Response {
  let errorMessage: string;
  let statusCode: number = status || 500;
  let reason: string | undefined;
  let errorDetails: unknown = details;

  if (error instanceof ApiError) {
    errorMessage = error.message;
    statusCode = error.statusCode;
    reason = error.reason;
    errorDetails = error.details || details;
  } else if (error instanceof Error) {
    errorMessage = error.message;
    // Try to determine status code from error message
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      statusCode = 401;
      reason = 'unauthorized';
    } else if (error.message.includes('token limit')) {
      reason = 'token_limit';
    }
  } else {
    errorMessage = error;
  }

  const body: ErrorResponse = { error: errorMessage };
  if (reason) body.reason = reason;
  if (errorDetails) body.details = errorDetails;
  
  return new Response(
    JSON.stringify(body),
    {
      status: statusCode,
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' }
    }
  );
}

/**
 * Handle common API errors
 */
export function handleApiError(error: unknown, context: string): Response {
  console.error(`[${context}] Error:`, error);
  
  if (error instanceof ApiError) {
    return createErrorResponse(error);
  }
  
  if (error instanceof Error) {
    // Handle authentication errors
    if (error.message.includes('401') || 
        error.message.includes('Unauthorized') || 
        error.message.includes('Invalid API key')) {
      return createErrorResponse(
        'API authentication failed. Please check your API key configuration.',
        401,
        { reason: 'api_auth_failed', details: error.message }
      );
    }
    
    // Handle token limit errors
    if (error.message.includes('token limit')) {
      return createErrorResponse(
        error.message,
        500,
        { reason: 'token_limit' }
      );
    }
    
    // Handle configuration errors
    if (error.message.includes('configuration') || error.message.includes('environment')) {
      return createErrorResponse(
        'Server configuration error',
        500,
        { reason: 'config_error', details: error.message }
      );
    }
    
    // Generic error with details
    return createErrorResponse(
      'An error occurred while processing your request',
      500,
      error.message
    );
  }
  
  // Unknown error type
  return createErrorResponse('An unexpected error occurred', 500);
}

/**
 * Wrap an async handler with error handling
 */
export function withErrorHandling(
  handler: (req: Request) => Promise<Response>,
  context: string
): (req: Request) => Promise<Response> {
  return async (req: Request) => {
    try {
      return await handler(req);
    } catch (error) {
      return handleApiError(error, context);
    }
  };
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T extends Record<string, unknown>>(
  data: T,
  headers?: Record<string, string>
): Response {
  return new Response(
    JSON.stringify(data),
    {
      headers: { 
        ...CORS_HEADERS, 
        'Content-Type': 'application/json',
        ...headers
      }
    }
  );
}

/**
 * Create a streaming response
 */
export function createStreamResponse(stream: ReadableStream<Uint8Array>): Response {
  return new Response(stream, {
    headers: { 
      ...CORS_HEADERS, 
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}