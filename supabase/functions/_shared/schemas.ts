/**
 * JSON Schema definitions for OpenAI structured outputs
 * These schemas ensure consistent response formats from LLMs
 */

export const TransliterationResponseSchema = {
  type: 'object',
  properties: {
    transliteration: {
      type: 'string',
      description: 'The transliterated text'
    }
  },
  required: ['transliteration'],
  additionalProperties: false
} as const;

export const TranslationResponseSchema = {
  type: 'object',
  properties: {
    translation: {
      type: 'string',
      description: 'The translated text'
    }
  },
  required: ['translation'],
  additionalProperties: false
} as const;

export const CombinedResponseSchema = {
  type: 'object',
  properties: {
    transliteration: {
      type: 'string',
      description: 'The transliterated text'
    },
    translation: {
      type: 'string',
      description: 'The translated text'
    }
  },
  required: ['transliteration', 'translation'],
  additionalProperties: false
} as const;

/**
 * OCR Response Schema for image text extraction
 */
export const OCRResponseSchema = {
  type: 'object',
  properties: {
    extractedText: {
      type: 'string',
      description: 'The text extracted from the image'
    }
  },
  required: ['extractedText'],
  additionalProperties: false
} as const;

/**
 * Type definitions derived from schemas
 */
export type TransliterationResponse = {
  transliteration: string;
};

export type TranslationResponse = {
  translation: string;
};

export type CombinedResponse = {
  transliteration: string;
  translation: string;
};

export type OCRResponse = {
  extractedText: string;
};