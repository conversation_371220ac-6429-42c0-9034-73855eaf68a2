import { createImageLL<PERSON>rovider, LLMProvider } from "./llm-providers.ts";
import type { EnvConfig } from "./env-config.ts";

/**
 * Temporary adapter layer.
 * While the broader refactor is in progress we just forward the
 * existing EnvConfig into the legacy `createImageLLMProvider` factory.
 *
 * Later, when `getEnvConfig()` is updated to expose an `image` sub-object,
 * this adapter can be rewritten to accept that narrower config.
 */
export function createImageProvider(config: EnvConfig): LLMProvider {
  return createImageLLMProvider(config);
}

export type { LLMProvider };
