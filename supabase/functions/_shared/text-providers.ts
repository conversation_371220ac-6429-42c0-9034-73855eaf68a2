import { createLLMProvider, LLMProvider } from "./llm-providers.ts";
import type { EnvConfig } from "./env-config.ts";

/**
 * Temporary adapter layer.
 * While the broader refactor is in progress we just forward the
 * existing EnvConfig into the legacy `createLLMProvider` factory.
 *
 * Later, when `getEnvConfig()` is updated to expose a `text` sub-object,
 * this adapter can be rewritten to accept that narrower config.
 */
export function createTextProvider(config: EnvConfig): LLMProvider {
  return createLLMProvider(config);
}

export type { LLMProvider };
