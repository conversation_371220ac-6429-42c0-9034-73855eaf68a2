/**
 * Shared validation utilities for all edge functions
 */

export type LanguageCode = 'ar' | 'en';

export interface ValidationResult {
  valid: boolean;
  error?: string;
}

/**
 * Validate language code
 */
export function validateLanguage(lang: unknown): lang is LanguageCode {
  return typeof lang === 'string' && ['ar', 'en'].includes(lang);
}

/**
 * Validate text input
 */
export function validateText(text: unknown): ValidationResult {
  if (typeof text !== 'string') {
    return { valid: false, error: 'Text must be a string' };
  }
  
  if (text.length === 0) {
    return { valid: false, error: 'Text cannot be empty' };
  }
  
  if (text.length > 10000) {
    return { valid: false, error: 'Text exceeds maximum length of 10,000 characters' };
  }
  
  return { valid: true };
}

/**
 * Validate image data (base64 or URL)
 */
export function validateImageData(imageData: string): ValidationResult {
  // Check if it's a base64 data URL
  if (imageData.startsWith('data:')) {
    const dataUrlRegex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
    if (!dataUrlRegex.test(imageData)) {
      return { valid: false, error: 'Invalid data URL format. Must be a base64-encoded image.' };
    }
    
    // Basic check for base64 content
    const base64Part = imageData.split(',')[1];
    if (!base64Part || base64Part.length < 100) {
      return { valid: false, error: 'Invalid or empty base64 image data.' };
    }
    
    return { valid: true };
  }
  
  // Check if it's a URL
  if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
    try {
      new URL(imageData);
      return { valid: true };
    } catch {
      return { valid: false, error: 'Invalid URL format.' };
    }
  }
  
  return { valid: false, error: 'Image data must be either a base64 data URL or a valid HTTP(S) URL.' };
}

/**
 * Validate audio data (base64 or URL)
 */
export function validateAudioData(audioData: string): ValidationResult {
  // Check if it's a base64 data URL
  if (audioData.startsWith('data:')) {
    const dataUrlRegex = /^data:audio\/(mpeg|mp3|wav|ogg);base64,/;
    if (!dataUrlRegex.test(audioData)) {
      return { valid: false, error: 'Invalid data URL format. Must be a base64-encoded audio.' };
    }
    
    // Basic check for base64 content
    const base64Part = audioData.split(',')[1];
    if (!base64Part || base64Part.length < 100) {
      return { valid: false, error: 'Invalid or empty base64 audio data.' };
    }
    
    return { valid: true };
  }
  
  // Check if it's a URL
  if (audioData.startsWith('http://') || audioData.startsWith('https://')) {
    try {
      new URL(audioData);
      return { valid: true };
    } catch {
      return { valid: false, error: 'Invalid URL format.' };
    }
  }
  
  return { valid: false, error: 'Audio data must be either a base64 data URL or a valid HTTP(S) URL.' };
}

/**
 * Generic request validation helper
 */
export function validateRequestFields<T extends Record<string, unknown>>(
  data: unknown,
  requiredFields: Array<keyof T>
): data is T {
  if (typeof data !== 'object' || data === null) {
    return false;
  }
  
  const obj = data as Record<string, unknown>;
  
  return requiredFields.every(field => {
    const value = obj[field];
    return value !== undefined && value !== null && value !== '';
  });
}