import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { getEnvConfig } from "../_shared/env-config.ts";
import { createImageLLMProvider, LLMProvider } from "../_shared/llm-providers.ts";
import { corsHeaders as CORS_HEADERS } from "../_shared/cors.ts";
import { OCRResponseSchema } from "../_shared/schemas.ts";
import { createInternalApiClient } from "../_shared/internal-api.ts";
import { validateLanguage, validateImageData, type LanguageCode } from "../_shared/validation.ts";
import { createErrorResponse, createSuccessResponse, createStreamResponse, ApiError } from "../_shared/error-handling.ts";

// Types
interface ImageTransliterationRequest {
  imageData: string; // base64 encoded image or URL
  sourceLang: LanguageCode;
  targetLang: LanguageCode;
  stream?: boolean;
}

interface ImageTransliterationResponse {
  extractedText: string;
  transliteration: string;
  translation: string;
}


// Types for streaming
type StreamEvent =
  | { stage: 'started' }
  | { stage: 'ocr_complete'; extractedText: string }
  | { type: 'transliteration_chunk'; delta: any }
  | { type: 'translation_chunk'; delta: any }
  | { stage: 'done' };

// Helper functions
function createOcrPrompt(sourceLang: LanguageCode, provider?: string): string {
  if (sourceLang === 'ar') {
    if (provider === 'openai') {
      return `Extract and return all Arabic text visible in the image exactly as written. CRITICAL: You must preserve ALL diacritical marks (تشكيل/tashkeel) including fatha, kasra, damma, sukun, shadda, tanween, and any other diacritics. Do not normalize or remove any Arabic diacritics. Return the text character-for-character as it appears in the image.`;
    }
    return `Extract and return all Arabic text visible in the image. Include all text exactly as it appears. Make sure to preserve diacritical marks / tashkeel if present.`;
  } else {
    return `Extract and return all English text visible in the image. Include all text exactly as it appears, preserving capitalization and punctuation.`;
  }
}


// Main handler
serve(async (req) => {
  console.log('[transliterate-image] Incoming request:', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries()),
  });
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      status: 200,
      headers: CORS_HEADERS 
    });
  }

  try {
    // Parse FormData from the request
    const formData = await req.formData();
    const image = formData.get('image') as File;
    const sourceLang = formData.get('sourceLang') as LanguageCode;
    const targetLang = formData.get('targetLang') as LanguageCode;
    const stream = formData.get('stream') === 'true';
    
    console.log('[transliterate-image] Request data:', {
      hasImage: !!image,
      imageSize: image?.size || 0,
      sourceLang,
      targetLang,
    });
    
    // Validate FormData fields
    if (!image || !sourceLang || !targetLang) {
      throw new ApiError('Missing required fields: image (file), sourceLang (ar|en), targetLang (ar|en)', 400);
    }

    // Validate language parameters
    if (!validateLanguage(sourceLang) || !validateLanguage(targetLang)) {
      throw new ApiError('Invalid language codes. Use "ar" for Arabic or "en" for English.', 400);
    }

    // Convert image file to base64 data URL
    const imageBuffer = await image.arrayBuffer();
    const uint8Array = new Uint8Array(imageBuffer);
    
    // Convert to base64 in chunks to avoid stack overflow
    let binaryString = '';
    const chunkSize = 8192;
    for (let i = 0; i < uint8Array.length; i += chunkSize) {
      const chunk = uint8Array.subarray(i, i + chunkSize);
      binaryString += String.fromCharCode(...chunk);
    }
    
    const imageBase64 = btoa(binaryString);
    const imageData = `data:${image.type};base64,${imageBase64}`;

    // Validate image data format
    const validation = validateImageData(imageData);
    if (!validation.valid) {
      throw new ApiError(validation.error!, 400);
    }

    const useStream = stream;

    // Get configuration
    let config;
    try {
      config = getEnvConfig();
      console.log('[transliterate-image] Config loaded:', {
        hasTextConfig: !!config.openrouter,
        hasImageConfig: !!config.image,
        imageProvider: config.image?.provider,
        imageModel: config.image?.model,
        textModel: config.openrouter?.model,
        hasApiKey: !!config.image?.apiKey,
        apiKeyLength: config.image?.apiKey?.length || 0
      });
    } catch (error) {
      console.error('[transliterate-image] Environment configuration error:', error);
      throw new ApiError('Server configuration error', 500, 'config_error');
    }

    // Check if image configuration is available
    if (!config.image) {
      throw new ApiError(
        'Image processing is not configured. Please set IMAGE_LLM_PROVIDER and IMAGE_MODEL environment variables.',
        500,
        'config_error'
      );
    }

    // Create image LLM provider instance
    let provider: LLMProvider;
    try {
      provider = createImageLLMProvider(config);
    } catch (error) {
      console.error('Failed to create image LLM provider:', error);
      throw new ApiError('Failed to initialize image LLM provider', 500, 'provider_error');
    }

    console.log('Processing image transliteration request:', {
      imageType: imageData.startsWith('data:') ? 'base64' : 'url',
      provider: config.image.provider,
      model: config.image.model,
      apiKeyLength: config.image.apiKey?.length || 0,
      apiKeyPrefix: config.image.apiKey?.substring(0, 10) || 'none',
      apiBaseUrl: config.image.apiBaseUrl
    });

    if (useStream) {
      const stream = new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          const enqueue = (data: StreamEvent) => {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
          };

          try {
            if (!provider.callAPIWithImage) {
              throw new Error('Image processing is not supported by the current provider');
            }

            enqueue({ stage: 'started' });

            // Step 1: Perform OCR with structured output
            const ocrPrompt = createOcrPrompt(sourceLang, config.image.provider);
            const ocrResponse = await provider.callAPIWithImage({
              prompt: ocrPrompt,
              imageData,
              responseFormat: {
                type: "json_schema",
                json_schema: {
                  name: "ocr_result",
                  schema: OCRResponseSchema,
                  strict: true
                }
              }
            });
            
            const ocrParsed = JSON.parse(ocrResponse);
            const extractedText = ocrParsed.extractedText;
            
            if (!extractedText) {
              throw new Error(`Failed to extract ${sourceLang === 'ar' ? 'Arabic' : 'English'} text from image`);
            }
            enqueue({ stage: 'ocr_complete', extractedText });

            // Step 2: Stream translation and transliteration in parallel
            const internalApi = createInternalApiClient();

            const transliterationStream = internalApi.streamTransliterate(extractedText, sourceLang);
            const translationStream = internalApi.streamTranslate(extractedText, sourceLang, targetLang);

            const processStream = async (stream: AsyncGenerator<any>, type: 'transliteration_chunk' | 'translation_chunk') => {
              for await (const event of stream) {
                enqueue({ type, delta: event });
              }
            };

            await Promise.all([
              processStream(transliterationStream, 'transliteration_chunk'),
              processStream(translationStream, 'translation_chunk')
            ]);
            
            enqueue({ stage: 'done' });
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (error) {
            const errorResponse = { error: error.message, details: error.stack };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: errorResponse })}\n\n`));
            controller.close();
          }
        },
      });
      return createStreamResponse(stream);
    } else {
      // Fallback to original non-streaming behavior
      try {
        if (!provider.callAPIWithImage) {
          throw new ApiError('Image processing is not supported by the current provider', 501, 'not_implemented');
        }

        // Step 1: Perform OCR to get text using structured output
        const ocrPrompt = createOcrPrompt(sourceLang, config.image.provider);
        const ocrResponse = await provider.callAPIWithImage({
          prompt: ocrPrompt,
          imageData,
          responseFormat: {
            type: "json_schema",
            json_schema: {
              name: "ocr_result",
              schema: OCRResponseSchema,
              strict: true
            }
          }
        });

        // Parse the OCR response (should be pure JSON with structured output)
        let extractedText: string;
        try {
          const ocrParsed = JSON.parse(ocrResponse);
          extractedText = ocrParsed.extractedText;
        } catch (error) {
          console.error('Failed to parse OCR response:', error, 'Response:', ocrResponse);
          throw new ApiError('Failed to parse OCR response', 500, 'parse_error');
        }

        if (!extractedText) {
          throw new ApiError(
            `Failed to extract ${sourceLang === 'ar' ? 'Arabic' : 'English'} text from image`,
            500,
            'ocr_failed'
          );
        }

        console.log('[transliterate-image] OCR extracted text:', extractedText);

        // Step 2: Call the existing translate and transliterate endpoints
        // This ensures code reuse and consistency across the application
        const internalApi = createInternalApiClient();
        
        try {
          const { translation, transliteration } = await internalApi.translateAndTransliterate(
            extractedText,
            sourceLang,
            targetLang
          );

          console.log('[transliterate-image] Transformation result:', { translation, transliteration });

          return createSuccessResponse({
            extractedText,
            transliteration,
            translation,
          });
        } catch (transformError) {
          console.error('[transliterate-image] Transformation error:', transformError);
          throw new ApiError(
            'Failed to transform extracted text',
            500,
            'transformation_failed',
            transformError instanceof Error ? transformError.message : transformError
          );
        }
      } catch (apiError) {
        // Re-throw ApiErrors to be handled by outer catch
        if (apiError instanceof ApiError) {
          throw apiError;
        }
        
        console.error('[transliterate-image] Image LLM API error:', apiError);
        
        if (apiError instanceof Error) {
          // Handle authentication errors from the API
          if (apiError.message.includes('401') || apiError.message.includes('Unauthorized') || apiError.message.includes('Invalid API key')) {
            throw new ApiError(
              'API authentication failed. Please check your IMAGE_API_KEY configuration.',
              401,
              'api_auth_failed',
              apiError.message
            );
          }
          
          // Handle token limit errors
          if (apiError.message.includes('token limit')) {
            throw new ApiError(apiError.message, 500, 'token_limit');
          }
          
          throw new ApiError(
            'An error occurred while processing the image',
            500,
            'api_error',
            apiError.message
          );
        }
        
        throw new ApiError('An unexpected error occurred during image transliteration', 500);
      }
    }
  } catch (error) {
    console.error('Error in transliterate-image function:', error);
    
    if (error instanceof ApiError) {
      return createErrorResponse(error.message, error.statusCode, { reason: error.reason, details: error.details });
    }
    
    if (error instanceof Error) {
      return createErrorResponse('An unexpected error occurred', 500, error.message);
    }
    
    return createErrorResponse('An unexpected error occurred', 500);
  }
});
