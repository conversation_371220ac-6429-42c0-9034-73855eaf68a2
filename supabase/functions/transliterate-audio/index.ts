import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { getEnvConfig } from "../_shared/env-config.ts";
import { createAudioProvider, LLMProvider } from "../_shared/audio-providers.ts";

// TODO: Import createTransliterationPrompt from "../_shared/transliteration-prompt.ts" when implementing audio processing

// Constants
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Types
interface AudioTransliterationRequest {
  audioData: string; // base64 encoded audio or URL
  stream?: boolean;
}

interface AudioTransliterationResponse {
  arabicText: string;
  transliteration: string;
  translation: string;
  audioUrl?: string; // URL for playback if available
}

interface ErrorResponse {
  error: string;
  details?: unknown;
}

// Helper functions
function createErrorResponse(error: string, status: number = 500, details?: unknown): Response {
  const body: ErrorResponse = { error };
  if (details) {
    body.details = details;
  }
  
  return new Response(
    JSON.stringify(body),
    {
      status,
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' }
    }
  );
}

function createSuccessResponse(data: AudioTransliterationResponse): Response {
  return new Response(
    JSON.stringify(data),
    {
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' }
    }
  );
}

function validateRequest(data: unknown): data is AudioTransliterationRequest {
  return (
    typeof data === 'object' &&
    data !== null &&
    'audioData' in data &&
    typeof (data as AudioTransliterationRequest).audioData === 'string' &&
    (data as AudioTransliterationRequest).audioData.length > 0
  );
}

function validateAudioData(audioData: string): { valid: boolean; error?: string } {
  // Check if it's a base64 data URL
  if (audioData.startsWith('data:')) {
    const dataUrlRegex = /^data:audio\/(mpeg|mp3|wav|ogg);base64,/;
    if (!dataUrlRegex.test(audioData)) {
      return { valid: false, error: 'Invalid data URL format. Must be a base64-encoded audio.' };
    }
    
    // Basic check for base64 content
    const base64Part = audioData.split(',')[1];
    if (!base64Part || base64Part.length < 100) {
      return { valid: false, error: 'Invalid or empty base64 audio data.' };
    }
    
    return { valid: true };
  }
  
  // Check if it's a URL
  if (audioData.startsWith('http://') || audioData.startsWith('https://')) {
    try {
      new URL(audioData);
      return { valid: true };
    } catch {
      return { valid: false, error: 'Invalid URL format.' };
    }
  }
  
  return { valid: false, error: 'Audio data must be either a base64 data URL or a valid HTTP(S) URL.' };
}

// Main handler
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      status: 200,
      headers: CORS_HEADERS 
    });
  }

  try {
    // Parse and validate request
    const requestData = await req.json();
    
    if (!validateRequest(requestData)) {
      return createErrorResponse('audioData is required and must be a string', 400);
    }

    const { audioData } = requestData;

    // Validate audio data format
    const validation = validateAudioData(audioData);
    if (!validation.valid) {
      return createErrorResponse(validation.error!, 400);
    }

    // Get configuration
    let config;
    try {
      config = getEnvConfig();
    } catch (error) {
      console.error('Environment configuration error:', error.message);
      return createErrorResponse('Server configuration error');
    }

    // Create audio provider instance
    let provider: LLMProvider;
    try {
      provider = createAudioProvider(config);
    } catch (error) {
      console.error('Failed to create audio provider:', error.message);
      return createErrorResponse('Audio processing is not yet implemented', 501);
    }

    // Placeholder for actual audio processing logic
    // This will be implemented when audio provider is ready
    return createErrorResponse('Audio processing is not yet implemented', 501);
  } catch (error) {
    console.error('Error in transliterate-audio function:', error);
    
    if (error instanceof Error) {
      return createErrorResponse(
        'An unexpected error occurred',
        500,
        error.message
      );
    }
    
    return createErrorResponse('An unexpected error occurred');
  }
});
