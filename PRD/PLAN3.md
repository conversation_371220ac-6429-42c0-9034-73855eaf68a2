# Plan: Implement OpenAI Structured Outputs Feature

## Problem Statement

The web application currently displays LLM responses as markdown code blocks (e.g., `\`\`\`json { "transliteration": "..." } \`\`\``) instead of properly parsed results. This occurs because:

1. OpenAI's structured outputs feature isn't properly configured
2. Response parsing doesn't handle markdown-wrapped JSON
3. The prompt detection logic is too restrictive
4. Some LLM providers return JSON wrapped in markdown formatting

## Solution Overview

Implement proper structured outputs support for OpenAI and add robust JSON parsing for all providers to ensure clean, properly formatted responses in the UI.

## Detailed Implementation Plan

### Phase 1: Core Infrastructure (Priority: HIGH)

#### 1.1 Create Shared Schema Definitions
**File**: `supabase/functions/_shared/schemas.ts` (NEW)

```typescript
// Define reusable JSON schemas for all response types
export const transliterationSchema = {
  name: "transliteration_result",
  schema: {
    type: "object",
    properties: {
      transliteration: { type: "string", description: "The transliterated text" }
    },
    required: ["transliteration"],
    additionalProperties: false
  },
  strict: true
};

export const translationSchema = {
  name: "translation_result",
  schema: {
    type: "object",
    properties: {
      translation: { type: "string", description: "The translated text" }
    },
    required: ["translation"],
    additionalProperties: false
  },
  strict: true
};

export const combinedSchema = {
  name: "transform_result",
  schema: {
    type: "object",
    properties: {
      transliteration: { type: "string", description: "The transliterated text" },
      translation: { type: "string", description: "The translated text" }
    },
    required: ["transliteration", "translation"],
    additionalProperties: false
  },
  strict: true
};
```

#### 1.2 Create JSON Parsing Utilities
**File**: `supabase/functions/_shared/response-parser.ts` (NEW)

```typescript
/**
 * Extracts JSON from various response formats including markdown code blocks
 */
export function extractJSON(text: string): string {
  // Remove markdown code blocks
  const codeBlockRegex = /```(?:json)?\s*\n?([\s\S]*?)\n?```/;
  const match = text.match(codeBlockRegex);
  
  if (match) {
    return match[1].trim();
  }
  
  // Check if the entire response is JSON
  const trimmed = text.trim();
  if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
    return trimmed;
  }
  
  // Try to find JSON within the text
  const jsonRegex = /(\{[\s\S]*\}|\[[\s\S]*\])/;
  const jsonMatch = text.match(jsonRegex);
  
  if (jsonMatch) {
    return jsonMatch[1];
  }
  
  return text;
}

/**
 * Safely parse JSON with fallback
 */
export function safeJSONParse<T>(text: string, fallback?: T): T | null {
  try {
    const extracted = extractJSON(text);
    return JSON.parse(extracted);
  } catch (error) {
    console.warn('Failed to parse JSON:', error, 'Text:', text);
    return fallback ?? null;
  }
}
```

#### 1.3 Update Response Parsing Functions
**Files to modify**:
- `supabase/functions/_shared/transliteration-prompt.ts`
- `supabase/functions/_shared/translation-prompt.ts`

Add import and update parsing:
```typescript
import { safeJSONParse } from './response-parser.ts';

export function parseTransliterationResponse(response: string): string {
  const parsed = safeJSONParse<{ transliteration: string }>(response);
  
  if (parsed?.transliteration) {
    return parsed.transliteration;
  }
  
  // Fallback for plain text responses
  return extractJSON(response).trim();
}
```

### Phase 2: Provider Updates (Priority: HIGH)

#### 2.1 Update LLM Provider Interfaces
**File**: `supabase/functions/_shared/llm-providers.ts`

Update the `LLMRequest` interface:
```typescript
export interface LLMRequest {
  prompt: string;
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
  };
  responseFormat?: {
    type: "json_schema";
    json_schema: {
      name: string;
      schema: any;
      strict?: boolean;
    };
  };
}
```

#### 2.2 Fix OpenAI Provider
**File**: `supabase/functions/_shared/llm-providers.ts`

Update the OpenAI provider's `callAPI` method:
```typescript
async callAPI(request: LLMRequest): Promise<Response> {
  const requestBody: any = {
    model: this.config.model,
    messages: [
      {
        role: 'user',
        content: request.prompt
      }
    ],
    temperature: request.generationConfig?.temperature ?? this.config.generationConfig.temperature,
    max_tokens: request.generationConfig?.maxOutputTokens ?? this.config.generationConfig.maxOutputTokens,
  };

  // Use structured outputs when responseFormat is provided
  if (request.responseFormat) {
    requestBody.response_format = request.responseFormat;
  } else if (request.prompt.toLowerCase().includes('json')) {
    // Auto-detect JSON requests
    requestBody.response_format = { type: "json_object" };
  }

  // ... rest of the method
}
```

#### 2.3 Add Structured Output Support to Other Providers
**OpenRouter Provider**: Add responseFormat support
**Gemini Provider**: Use function calling for structured outputs

### Phase 3: Edge Function Updates (Priority: MEDIUM)

#### 3.1 Update Edge Functions
**Files to modify**:
- `supabase/functions/transliterate/index.ts`
- `supabase/functions/translate/index.ts`

Import schemas and update provider calls:
```typescript
import { transliterationSchema } from '../_shared/schemas.ts';

// In the API call
const response = await provider.callAPI({
  prompt,
  responseFormat: {
    type: "json_schema",
    json_schema: transliterationSchema
  }
});
```

### Phase 4: Testing & Validation (Priority: MEDIUM)

#### 4.1 Create Test Suite
**File**: `tests/test-structured-outputs.js` (NEW)

Test scenarios:
1. OpenAI with structured outputs
2. Fallback providers without structured outputs
3. Markdown-wrapped JSON responses
4. Plain text responses
5. Error cases and refusals

## Implementation Sequence

1. **Quick Fix (Immediate)**
   - Implement `response-parser.ts` with JSON extraction
   - Update parsing functions to use the new parser
   - This will immediately fix the display issue

2. **Full Implementation (1-2 days)**
   - Create schema definitions
   - Update all providers
   - Modify edge functions
   - Add comprehensive tests

## Expected Outcomes

1. **Immediate**: Users will no longer see JSON code blocks
2. **Short-term**: More reliable JSON responses from all providers
3. **Long-term**: Type-safe, validated responses with better error handling

## Risk Mitigation

1. **Backward Compatibility**: Keep existing parsing as fallback
2. **Provider Differences**: Test with each provider before deployment
3. **Error Handling**: Add detailed logging for debugging
4. **Gradual Rollout**: Deploy to staging first

## Success Metrics

1. Zero JSON code blocks displayed to users
2. 100% valid JSON responses from OpenAI
3. Improved response parsing success rate
4. Reduced error rates in production

## Notes

- OpenAI's structured outputs require specific models (gpt-4o-2024-08-06 or later)
- Not all providers support structured outputs natively
- The response parser should handle various edge cases gracefully