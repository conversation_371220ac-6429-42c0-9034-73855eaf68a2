# PLAN2: Separate LLM API Calls for Transliteration & Translation

## Overview
Refactor the application to use separate, parallel LLM API calls for transliteration and translation operations, allowing each to return independently while maintaining the current user experience.

## 1. Architecture Changes

### 1.1 API Structure
Transform from a **unified API call** to **parallel independent calls**:

```mermaid
graph TD
    A[User Submits Text] --> B[Frontend Service Layer]
    B --> C[Parallel API Requests]
    
    C --> D[/transliterate]
    C --> E[/translate]
    
    D --> F[Transliteration Response]
    E --> G[Translation Response]
    
    F --> H[Update UI - Transliteration]
    G --> I[Update UI - Translation]
    
    style C fill:#f9f,stroke:#333,stroke-width:4px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
```

### 1.2 Edge Functions

#### Modified Functions:
- **`/functions/v1/transliterate`** - Returns ONLY transliteration (overwrites existing)
- **`/functions/v1/translate`** - NEW endpoint for translation only

#### Shared Utilities:
- `_shared/transliteration-prompt.ts` - Dedicated transliteration prompts
- `_shared/translation-prompt.ts` - NEW: Dedicated translation prompts
- `_shared/pipeline.ts` - Update to support separate operations

## 2. Type Contracts Update

### 2.1 Request/Response Types
```typescript
// src/shared/types/api.types.ts

// Existing types remain for backward compatibility
export interface TransformRequest {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  text: string;
}

export interface TransformResponse {
  transliteration: string;
  translation: string;
}

// NEW: Separate types for individual operations
export interface TransliterationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  stream?: boolean;
}

export interface TransliterationResponse {
  transliteration: string;
}

export interface TranslationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  stream?: boolean;
}

export interface TranslationResponse {
  translation: string;
}

// Operation status for UI state management
export interface OperationStatus<T> {
  status: 'idle' | 'loading' | 'success' | 'error';
  data?: T;
  error?: string;
}
```

## 3. Backend Implementation

### 3.1 Edge Function: `/transliterate`
```typescript
// supabase/functions/transliterate/index.ts
// OVERWRITES existing function - now returns ONLY transliteration

import { createTransliterationPrompt } from "../_shared/transliteration-prompt.ts";

interface TransliterationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  stream?: boolean;
}

interface TransliterationResponse {
  transliteration: string;
  original: string;
}

// Handler returns ONLY transliteration
// Supports both streaming (SSE) and non-streaming responses
```

### 3.2 Edge Function: `/translate` (NEW)
```typescript
// supabase/functions/translate/index.ts

import { createTranslationPrompt } from "../_shared/translation-prompt.ts";

interface TranslationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  stream?: boolean;
}

interface TranslationResponse {
  translation: string;
  original: string;
}

// Handler returns ONLY translation
// Supports both streaming (SSE) and non-streaming responses
```

### 3.3 Prompt Functions
```typescript
// supabase/functions/_shared/transliteration-prompt.ts
export function createTransliterationPrompt(
  text: string,
  sourceLang: 'ar' | 'en'
): string {
  // Returns prompt for ONLY transliteration
  // Arabic → Latin or English → Arabic romanization
}

// supabase/functions/_shared/translation-prompt.ts (NEW)
export function createTranslationPrompt(
  text: string,
  sourceLang: 'ar' | 'en',
  targetLang: 'ar' | 'en'
): string {
  // Returns prompt for ONLY translation
  // Arabic ↔ English translation
}
```

### 3.4 Update Pipeline
```typescript
// supabase/functions/_shared/pipeline.ts
export async function processTransliteration(
  text: string,
  sourceLang: 'ar' | 'en',
  provider: LLMProvider
): Promise<string>

export async function processTranslation(
  text: string,
  sourceLang: 'ar' | 'en',
  targetLang: 'ar' | 'en',
  provider: LLMProvider
): Promise<string>

// Keep existing transform() for image processing compatibility
```

## 4. Frontend Implementation

### 4.1 Service Layer Updates

#### TextLLMService.ts Enhancement
```typescript
// src/services/TextLLMService.ts

export class TextLLMService {
  private transliterationController: AbortController | null = null;
  private translationController: AbortController | null = null;

  /**
   * Get transliteration only
   */
  async getTransliteration(
    request: TransliterationRequest,
    onChunk?: (text: string) => void,
    onComplete?: (text: string) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // Call /functions/v1/transliterate
    // Handle streaming if requested
  }

  /**
   * Get translation only
   */
  async getTranslation(
    request: TranslationRequest,
    onChunk?: (text: string) => void,
    onComplete?: (text: string) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // Call /functions/v1/translate
    // Handle streaming if requested
  }

  /**
   * Get both in parallel (new primary method)
   */
  async getTransformParallel(
    request: TransformRequest,
    callbacks: {
      onTransliterationChunk?: (text: string) => void;
      onTransliterationComplete?: (text: string) => void;
      onTransliterationError?: (error: Error) => void;
      onTranslationChunk?: (text: string) => void;
      onTranslationComplete?: (text: string) => void;
      onTranslationError?: (error: Error) => void;
    }
  ): Promise<void> {
    // Launch both requests in parallel
    // Handle responses independently
  }

  /**
   * Abort specific operations
   */
  abortTransliteration(): void
  abortTranslation(): void
  abortAll(): void
}
```

### 4.2 Hook Updates

#### useTransliteration Hook Refactor
```typescript
// src/shared/hooks/useTransliteration.ts

interface UseTransliterationReturn {
  messages: Message[];
  isLoading: boolean;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
  addMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  retryTransliteration: (messageId: string) => Promise<void>;
  retryTranslation: (messageId: string) => Promise<void>;
}

// Update to handle parallel operations with independent status tracking
```

### 4.3 Component Updates

#### TextTransformForm.tsx
```typescript
// src/features/text/TextTransformForm.tsx

// Update Message interface
interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  transliteration?: string;
  translation?: string;
  original?: string;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
}

// Update to show independent loading states
// Display results as they arrive
// Allow retry of individual failed operations
```

## 5. Image Processing Updates

### 5.1 ImageOCRService
```typescript
// src/services/ImageOCRService.ts

// Update to use parallel calls after OCR
export class ImageOCRService {
  async processImage(
    file: File,
    sourceLang: 'ar' | 'en',
    targetLang: 'ar' | 'en',
    onProgress: (stage: ProcessingStage, progress: number) => void
  ): Promise<ImageProcessingResult> {
    // 1. Upload & OCR (existing)
    // 2. Call transliteration and translation in parallel
    // 3. Return results as they complete
  }
}
```

### 5.2 Update Processing Stages
```typescript
export type ProcessingStage = 
  | 'uploading' 
  | 'ocr' 
  | 'transliterating'  // NEW: separate stage
  | 'translating'      // NEW: separate stage
  | 'done';

// Progress percentages:
// uploading: 0-15%
// ocr: 15-40%
// transliterating: 40-70%
// translating: 40-70% (parallel with transliterating)
// done: 100%
```

## 6. UI/UX Enhancements

### 6.1 Loading States
- Show independent spinners for transliteration and translation
- Use skeleton loaders that match expected content size
- Animate transitions when results arrive

### 6.2 Error Handling
- Display inline error messages for failed operations
- Provide retry buttons for individual operations
- Show partial results when one operation succeeds

### 6.3 Result Display
```typescript
// ResultCard updates
<ResultCard title="Transliteration">
  {transliterationStatus.status === 'loading' && <LoadingSpinner />}
  {transliterationStatus.status === 'error' && (
    <ErrorMessage 
      message={transliterationStatus.error} 
      onRetry={retryTransliteration}
    />
  )}
  {transliterationStatus.status === 'success' && (
    <div>{transliterationStatus.data}</div>
  )}
</ResultCard>
```

## 7. Implementation Steps (Single Day Execution)

### Step 1: Backend Implementation (2-3 hours)
1. Create `/translate` edge function
2. Update `/transliterate` to return only transliteration
3. Create `translation-prompt.ts`
4. Update `pipeline.ts` for separate operations
5. Quick test of edge functions with curl/Postman

### Step 2: Frontend Service Layer (2 hours)
1. Update `TextLLMService` with parallel methods
2. Update type definitions in `api.types.ts`
3. Add basic error handling for partial failures
4. Keep existing methods for backward compatibility

### Step 3: UI Component Updates (2-3 hours)
1. Update `useTransliteration` hook for parallel operations
2. Modify `TextTransformForm` to show independent results
3. Update `ImageOCRService` for parallel calls after OCR
4. Add basic retry functionality

### Step 4: Testing & Deployment (1-2 hours)
1. Manual testing of text transformation flow
2. Manual testing of image transformation flow
3. Deploy edge functions to development environment
4. Deploy frontend changes
5. Smoke test in production

## 8. Performance Considerations

### 8.1 Request Optimization
- Use HTTP/2 multiplexing for parallel requests
- Implement request deduplication
- Add client-side caching with React Query

### 8.2 Response Handling
- Stream responses when possible
- Use Web Workers for parsing large responses
- Implement progressive rendering

### 8.3 Error Recovery
- Exponential backoff for retries
- Circuit breaker pattern for failing endpoints
- Fallback to cached results when available

## 9. Monitoring & Analytics

### 9.1 Metrics to Track
- Individual operation latency
- Success/failure rates per operation
- User retry patterns
- Partial success scenarios

### 9.2 Logging
```typescript
// Log structure for parallel operations
{
  requestId: string,
  userId?: string,
  operations: {
    transliteration: {
      status: 'success' | 'error',
      duration: number,
      error?: string
    },
    translation: {
      status: 'success' | 'error',
      duration: number,
      error?: string
    }
  },
  totalDuration: number
}
```

## 10. Migration Strategy (Single Day)

### 10.1 Direct Implementation
Since this is a single-day implementation:
- No feature flags needed for initial deployment
- Direct cutover to new parallel API structure
- Keep error handling to gracefully handle any issues

### 10.2 Backward Compatibility
- Frontend will exclusively use new parallel endpoints
- Remove dependency on unified response format
- Clean implementation without migration complexity

### 10.3 Deployment Strategy
1. Deploy backend changes first (edge functions)
2. Test endpoints manually
3. Deploy frontend changes
4. Monitor for any issues
5. Quick fixes if needed

## 11. Benefits Summary

1. **Improved User Experience**
   - 30-50% faster perceived performance
   - Partial results better than waiting
   - Clear operation status

2. **Better Reliability**
   - Isolated failure domains
   - Granular retry capability
   - Improved error messages

3. **Operational Benefits**
   - Independent scaling
   - Model optimization per operation
   - Cost optimization potential

4. **Developer Experience**
   - Cleaner separation of concerns
   - Easier to test and debug
   - More flexible architecture

## 12. Execution Priority (Single Day)

### Critical Path (Must Complete)
1. Backend: Create `/translate` endpoint and update `/transliterate`
2. Frontend: Update `TextLLMService` for parallel calls
3. UI: Update `TextTransformForm` to display independent results

### Nice to Have (If Time Permits)
1. Retry functionality for individual operations
2. Enhanced loading animations
3. Performance monitoring setup
4. Comprehensive error messages
5. Image processing parallel updates

### Can Defer
1. Advanced caching strategies
2. Analytics implementation
3. A/B testing infrastructure
4. Web Worker optimizations
5. Circuit breaker patterns

### Quick Wins
1. Basic error handling with clear messages
2. Simple loading states (no fancy animations)
3. Manual testing instead of automated tests
4. Direct deployment without gradual rollout

---

_Last updated: 2025-06-22_