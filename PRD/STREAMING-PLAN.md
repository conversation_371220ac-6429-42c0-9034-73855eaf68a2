# Real-time JSON Streaming Plan

## Scope
Enable streaming for all text and image pipelines (`transliterate`, `translate`, `transliterate-image`) while leaving audio generation non-streaming.

## 2. Current Flow
User → React (`UnifiedTransformForm`) → `TextLLMService` → Edge Function → LLM provider  
Edge waits for full completion and returns the final JSON; the UI renders only after the entire response arrives.

## 3. Target Streaming Architecture
```mermaid
sequenceDiagram
  participant UI as React UI
  participant SVC as TextLLMService
  participant FN as Edge Function
  participant LLM as LLM Provider

  UI->>SVC: request(stream=true)
  SVC->>FN: POST stream=true (SSE)
  FN->>LLM: openai.beta.chat.completions.stream(...)
  loop per chunk
    LLM-->>FN: content.delta (parsed)
    FN-->>SVC: SSE { delta }
    SVC-->>UI: onChunk(merge(delta))
  end
  LLM-->>FN: content.done
  FN-->>SVC: SSE { done }
  SVC-->>UI: onComplete(final JSON)
```

## 4. Edge-Function Changes
* Accept `stream?: boolean` (default `false` for back-compat).  
* If `stream` is `true`  
  1. Call `client.beta.chat.completions.stream(...)` using the same JSON schema.  
  2. Wrap the async generator in a `ReadableStream` that emits SSE lines:  
     * `data: {"delta": <partial>}\n\n` for every `content.delta` with `event.parsed`.  
     * After `content.done` emit `data: {"done": <final>}\n\n` then `data: [DONE]\n\n`.  
  3. Reuse helper `createSSEResponse(stream)` with proper headers.  
* On error emit `data: {"error": {code, message}}\n\n` before closing.

## 5. Service-Layer Changes (`src/services/TextLLMService.ts`)
* Add utility `deepMergePartial<T>()` to merge partial JSON into an accumulator.  
* When `content-type` is `text/event-stream`:  
  * For `{ delta }` → merge & invoke `onChunk(accumulator)`.  
  * For `{ done }` → invoke `onComplete(done)`.  
* Preserve non-streaming path when `stream=false`.

## 6. React UI Updates
* Pass `stream=true` from `UnifiedTransformForm`.  
* Extend message state with `partialTransliteration` / `partialTranslation`.  
* Render grey placeholders or spinners until fields arrive.  
* Provide a “Stop” button that aborts via `AbortController`.

## 7. Feature Flag & Roll-out
* Frontend ENV: `VITE_STREAM_LLM=true`.  
* Roll-out sequence: development → staging (monitor) → production.  
* Flip flag only after 48 h of error-free logs.

## 8. Error Handling
* Edge emits `{ error }` SSE and closes.  
* Client shows toast, leaves any partial data visible with a “partial” banner.

## 9. Testing Matrix
| Layer      | Test                                                         |
|------------|--------------------------------------------------------------|
| Unit (Deno)| Mock OpenAI stream → assert SSE serialization                |
| Unit (TS)  | `deepMergePartial` correctness                                |
| Cypress    | Slow network → progressive UI updates                        |
| Manual     | Image pipeline stages still display correctly                |

## 10. Deployment Checklist
- [x] Provider `streamStructured` helper
- [x] Update `transliterate`, `translate`, `transliterate-image` edge functions
- [x] Implement `deepMergePartial` + service upgrades
- [x] UI skeletons & abort button
- [ ] `docs/streaming.md` explaining new contract
- [ ] Enable env flag in staging, monitor Supabase + client logs
- [ ] Promote to production & remove old non-streaming fallbacks after validation

-- End --