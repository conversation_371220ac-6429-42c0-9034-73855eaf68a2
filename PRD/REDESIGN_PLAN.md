# SalahScribe Frontend Redesign Plan

## Overview
This document outlines the complete redesign of the SalahScribe frontend, transforming the current three-tab interface (Text, Image, Audio) with nested language tabs into a simpler two-tab interface (Arabic→English, English→Arabic) with multiple input methods accessible via buttons at the bottom of the interface.

## Current vs Proposed Architecture

### Current UI Structure
- Three top-level tabs: Text Input, Image Upload, Audio (disabled)
- Each tab has two sub-tabs for language direction
- Text input uses a chat bubble interface
- Image upload uses a separate form with card-based results

### Proposed UI Structure
```
┌─────────────────────────────────────────┐
│  [Arabic → English] [English → Arabic]  │  ← Main tabs
├─────────────────────────────────────────┤
│                                         │
│          Chat Interface                 │  ← Unified chat area
│          (Messages)                     │
│                                         │
├─────────────────────────────────────────┤
│  [Text Input Area]                      │
│  [📤][📷][🎤][🎙️]                      │  ← Input buttons
└─────────────────────────────────────────┘
```

## Implementation Plan

### Phase 1: Restructure Tab Navigation
1. Modify `Index.tsx`:
   - Remove the three top-level tabs (Text, Image, Audio)
   - Keep only language direction tabs (Arabic→English, English→Arabic)
   - Remove nested tab structure

### Phase 2: Create Unified Transform Component
1. Create `UnifiedTransformForm.tsx`:
   - Merge functionality from TextTransformForm and ImageTransformForm
   - Implement unified chat bubble interface
   - Handle multiple input types (text, image, future audio)

2. Message Type Structure:
```typescript
interface UnifiedMessage {
  id: string;
  type: 'user' | 'assistant';
  inputType: 'text' | 'image' | 'audio';
  content: string;
  imageUrl?: string; // For image messages
  timestamp: Date;
  transliteration?: string;
  translation?: string;
  original?: string;
  extractedText?: string; // For OCR results
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
}
```

### Phase 3: Implement Multi-Input Interface
1. Bottom Input Area:
   - Text input field (existing)
   - Send button (existing)
   - Image upload button (new position)
   - Audio upload button (disabled)
   - Live audio button (disabled)

### Phase 4: Image Processing Integration
1. Image Message Display:
   - Show image thumbnail in chat bubble
   - Display OCR progress inline
   - Show results (extracted text, transliteration, translation) as nested chat bubbles

### Phase 5: Styling Updates
1. Unified Color Scheme:
   - Maintain existing emerald/slate theme
   - Consistent chat bubble styling
   - Disabled state for audio buttons

## File Structure
```
src/
├── pages/
│   └── Index.tsx (simplified)
├── features/
│   └── transform/
│       ├── UnifiedTransformForm.tsx (new)
│       ├── components/
│       │   ├── ChatMessage.tsx
│       │   ├── InputArea.tsx
│       │   └── MessageBubble.tsx
│       └── types/
│           └── message.types.ts
```

## Key Features
1. **Unified Chat Interface**: All input types (text, image, audio) use the same chat bubble interface
2. **Simplified Navigation**: Only two main tabs for language direction
3. **Bottom Action Bar**: Easy access to different input methods
4. **Consistent Results**: OCR results appear as chat bubbles, matching text input behavior
5. **Future-Ready**: Audio buttons are visible but disabled, ready for future implementation

## Migration Steps
1. Create new UnifiedTransformForm component
2. Test with text input only
3. Integrate image processing
4. Update Index.tsx to use new structure
5. Remove old components
6. Add disabled audio buttons

## Benefits
- Simplified user experience
- Better mobile accessibility
- Consistent interface across all input types
- Easier to maintain and extend
- Ready for future audio features