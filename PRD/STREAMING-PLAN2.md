# Final Plan: Image Stream Implementation

1.  **Goal:** Refactor the `transliterate-image` edge function to stream translation and transliteration results independently after the OCR step, creating a user experience consistent with the text submission flow.

2.  **Core Strategy:**
    *   Use the `ImageLLMProvider` for the initial, non-streaming OCR call to extract text.
    *   Once text is extracted, use the existing `TextProvider` infrastructure (via the `InternalApi`) to perform the translation and transliteration, leveraging its `streamStructured` async generator capabilities.

3.  **Step-by-Step Implementation:**

    *   **`supabase/functions/_shared/internal-api.ts`:**
        *   Create two new `async function*` (async generator) methods: `streamTranslate` and `streamTransliterate`.
        *   These methods will instantiate a `TextProvider` and call its `provider.streamStructured` method, yielding each event from the provider's stream. This will effectively proxy the stream of `delta` objects.

    *   **`supabase/functions/transliterate-image/index.ts`:**
        *   After the successful OCR call and sending the `ocr_complete` event, replace the current blocking call.
        *   Create two parallel processes that consume the new async generator methods from the internal API.
        *   One process will iterate through `internalApi.streamTranslate()` and enqueue `translation_chunk` events to the client.
        *   The other process will iterate through `internalApi.streamTransliterate()` and enqueue `transliteration_chunk` events.
        *   `Promise.all()` will be used to wait for both streams to finish before sending the `[DONE]` message.

    *   **`src/services/ImageOCRService.ts`:**
        *   Update the stream-handling logic in `processImage` to recognize the new `translation_chunk` and `transliteration_chunk` event types.
        *   It will incrementally build the `translation` and `transliteration` strings and dispatch `partial` events to the UI layer via the `onEvent` callback.

    *   **`src/hooks/useOCR.ts`:**
        *   Review the hook to ensure its state management correctly handles the rapid `partial` updates to provide a smooth user experience.

This approach reuses the existing, robust text-streaming architecture, ensuring consistency and maintainability.