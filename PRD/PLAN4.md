# Post-Translation Transliteration Implementation Plan

This document outlines the plan to implement the "post-translation transliteration" feature.

## High-Level Overview

The goal is to add a third generated text bubble that contains a transliteration of the *translated* text.

**If user enters Arabic:**
1.  **Arabic Transliteration:** Transliterate the original Arabic text into English characters (using Prompt 1).
2.  **Translation:** Translate the original Arabic text into English.
3.  **English Transliteration:** Transliterate the *result* of the translation from step 2 into Arabic characters (using Prompt 2).

**If user enters English:**
1.  **English Transliteration:** Transliterate the original English text into Arabic characters (using Prompt 2).
2.  **Translation:** Translate the original English text into Arabic.
3.  **Arabic Transliteration:** Transliterate the *result* of the translation from step 2 into English characters (using Prompt 1).

This creates a small sequential dependency: step 3 must wait for step 2 to complete. Steps 1 and 2 can still run in parallel.

## Implementation Strategy: Client-Side Orchestration (Option A)

We will orchestrate the new sequential step on the client-side to minimize backend changes and leverage the existing frontend state management and service architecture.

### 1. Frontend Type Definitions

-   **File:** [`src/features/transform/types/message.types.ts`](src/features/transform/types/message.types.ts)
-   **Changes:**
    -   Add a new optional property `postTransliteration?: string;` to the `UnifiedMessage` interface to store the result of the third operation.
    -   Add a new status tracker `postTransliterationStatus: OperationStatus<string>;` to the `UnifiedMessage` interface to manage the loading/error state of the new operation independently.

### 2. Frontend Service Layer

-   **File:** [`src/services/TextLLMService.ts`](src/services/TextLLMService.ts)
-   **Changes:**
    -   Create a new exported async function `getPostTransliteration`.
    -   This function will take the translated text and the *original* source language as arguments.
    -   It will call `createTransliterationPrompt` with the appropriate `sourceLang` to generate the correct prompt (e.g., if original was 'ar', the new source is 'en').

### 3. Frontend Orchestration (React Hook)

-   **File:** [`src/features/transform/UnifiedTransformForm.tsx`](src/features/transform/UnifiedTransformForm.tsx)
-   **Changes:**
    -   Create a new `useMutation` hook from React Query for the `getPostTransliteration` service call (`postTransliterationMutation`).
    -   In the existing `translationMutation`, add an `onSuccess` callback.
    -   Inside the `onSuccess` callback of the `translationMutation`, trigger `postTransliterationMutation.mutate()` with the translated text. This creates the sequential dependency.
    -   The initial `transliterationMutation` and `translationMutation` will continue to be called in parallel as they are now.

### 4. UI Components

-   **File:** [`src/features/transform/UnifiedTransformForm.tsx`](src/features/transform/UnifiedTransformForm.tsx)
-   **Changes:**
    -   Add a third `ResultCard` component to display the `postTransliteration` result.
    -   This new card's visibility and content will be driven by the `postTransliterationStatus` object (showing a loading spinner, the result, or an error with a retry button).
    -   The title of the card should be dynamic, e.g., "English Transliteration" or "Arabic Transliteration".

### 5. Feature Flagging

-   The new UI elements and the `onSuccess` logic will be wrapped in a feature flag check, e.g., `if (import.meta.env.VITE_FEATURE_POST_TRANSLITERATION === 'true') { ... }`. This allows for safe deployment.

## File Manifest

Files to be modified:

1.  `src/features/transform/types/message.types.ts`
2.  `src/services/TextLLMService.ts`
3.  `src/features/transform/UnifiedTransformForm.tsx`
4.  `.env.development.example` (to add the new feature flag)