# PLAN: SalahScribe Text & Image Refactor

## 1. Goal  
Provide two fully-featured modalities—**Text Input** and **Image Upload**—each with bidirectional Arabic ↔ English transformation (transliteration + translation) and audio playback generated via GPT-4o Mini Audio. The **Audio Input** modality will be handled in a future milestone.

## 2. UX & Routing  
* Top-level Shadcn `Tabs` in [`src/app/App.tsx`](src/app/App.tsx:1):  
  * **Text** (`/text`)  
  * **Image** (`/image`)  
  * **Audio** (placeholder, disabled)  
* Each tab contains **two sub-tabs**:  
  * Arabic ➜ English (`/text/ar-en`, `/image/ar-en`)  
  * English ➜ Arabic (`/text/en-ar`, `/image/en-ar`)  
* Nested React-Router `<Outlet>` keeps URLs shareable; sub-tab selection persists on reload.  
* Mobile: sub-tabs horizontal; ≥md screens: secondary vertical tab list.

```mermaid
graph TD
  App --> Tabs
  Tabs --> TextTab
  Tabs --> ImageTab
  TextTab --> TextArEn
  TextTab --> TextEnAr
  ImageTab --> ImageArEn
  ImageTab --> ImageEnAr
```

## 3. Component Hierarchy  
* `features/text/TextPage.tsx`  
  * `TextTransformForm` – textarea + submit  
* `features/image/ImagePage.tsx`  
  * `ImageTransformForm` – drag-and-drop + preview  
* `shared/components/ResultCard.tsx` (extend)  
  * Original text block  
  * Transliterated text block  
  * Translated text block  
  * `IconButton` (play original)  
  * `IconButton` (play translated)  
* `shared/components/ui/tabs.tsx` – add nested orientation prop

## 4. Hooks & State  
* `useTransliteration` (refactor) – orchestrates text flow  
* `useOCR` (new) – image ➜ text via Edge Function  
* `useTTS` (new) – memoises ArrayBuffer blobs; exposes `play()` helper  
* `use-toast` – reused for errors and status  
* React-Query retains caching; set `staleTime` = 5 min for OCR/LLM

## 5. Front-end Service Layer  
| Service | Endpoint | Notes |  
|---------|----------|-------|  
| `TextLLMService.ts` | `POST /supabase/functions/transliterate` | SSE streaming |  
| `ImageOCRService.ts` | `POST /supabase/functions/transliterate-image` | JSON |  
| `AudioTTSService.ts` | `POST /supabase/functions/audio` | Binary (Uint8Array) |  

Common helper `parseLLMResponse()` in `_shared/pipeline.ts`.

### Type Contracts `shared/types/api.types.ts`
```ts
export interface TransformRequest {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  text: string;
}

export interface TransformResponse {
  transliteration: string;
  translation: string;
}

export interface AudioRequest {
  text: string;
  lang: 'ar' | 'en';
}

export type AudioResponse = ArrayBuffer; // PCM or mp3
```

## 6. Supabase Edge Functions  
* `_shared/llm-providers.ts` – ensure GPT-4o provider key `"gpt4o"`  
* `_shared/audio-providers.ts` – add GPT-4o Mini client (no DB write)  
* `_shared/pipeline.ts` (new) – `transform(text, src, tgt)` reused by both handlers  
* `transliterate/index.ts` – delegate to pipeline, stream `{transliteration, translation}` chunks  
* `transliterate-image/index.ts` – pipeline: OCR ➜ transform  

## 7. File / Directory Additions
```
src/features/
  text/
    TextPage.tsx
    TextTransformForm.tsx
  image/
    ImagePage.tsx
    ImageTransformForm.tsx
src/hooks/useOCR.ts
src/hooks/useTTS.ts
src/services/TextLLMService.ts
src/services/ImageOCRService.ts
src/services/AudioTTSService.ts
supabase/functions/_shared/pipeline.ts
```

## 8. Styling  
* Tailwind `transition-[height]` for smooth card expansion  
* Extend color tokens for play buttons (`brandAccent`)  
* Dark-mode aware waveform icons

## 9. Environment Variables  
Update `/config/.env.*.example` files to mirror existing pattern:

```env
### Audio Processing
AUDIO_LLM_PROVIDER=gpt4o               # Provider id, matches _shared/audio-providers
AUDIO_API_KEY=sk_live_xxx              # Required
AUDIO_MODEL=google/gpt-4o-mini-audio
AUDIO_TEMPERATURE=0.3
AUDIO_MAX_OUTPUT_TOKENS=4096
```
* Remove previously proposed `VITE_AUDIO_MODEL`.  
* Include the new keys in `shared/utils/env-validation.ts` and `supabase/functions/_shared/env-config.ts`.

## 10. Incremental Implementation Steps  
1. **Routing skeleton** – tabs + placeholder pages  
2. **Extract new service classes**; unit-test with mock `fetch`  
3. **Migrate existing text flow** ➜ new services/hooks/UI  
4. **Audio playback** – integrate GPT-4o Mini, add `useTTS`  
5. **OCR pathway** – implement Edge Function + `useOCR`; wire Image tab  
6. **ENV & validation** – commit example files  
7. **Styling polish & accessibility**  
8. **E2E tests** – Cypress user flow for both Text & Image

## 11. Risks & Mitigations  

| Risk | Mitigation |
|------|------------|
| GPT-4o latency | Pre-fetch audio on result render; cache blobs in IndexedDB |
| SSE disconnects on Vercel | Send 15 s keep-alive headers; verify in staging |
| Large image sizes | Enforce 3 MB client limit; compress client-side |
| Mobile drag-and-drop quirks | Fallback to `<input type="file">` |

## 12. Future / Out-of-Scope  
* Audio Input modality  
* Persisting generated audio in Supabase Storage  
* Multi-speaker TTS options

---

_Last updated: 2025-06-16_