#!/usr/bin/env node

/**
 * Deploy environment variables to Supabase Edge Functions
 * This script automates the process of setting secrets for edge functions
 * Supports multiple environments: development (.env.development), staging (.env.staging), and production (.env.production)
 * Usage: 
 *   node scripts/deploy-env.js                   # Deploy to production
 *   node scripts/deploy-env.js --env=dev         # Deploy to development
 *   node scripts/deploy-env.js --env=staging     # Deploy to staging
 *   node scripts/deploy-env.js --env=prod        # Deploy to production
 *   npm run supabase:dev:env                     # Deploy to development
 *   npm run supabase:staging:env                 # Deploy to staging
 *   npm run supabase:prod:env                    # Deploy to production
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Console colors
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function error(message) {
  console.error(`${colors.red}❌ ${message}${colors.reset}`);
}

function success(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function info(message) {
  console.log(`${colors.cyan}ℹ️  ${message}${colors.reset}`);
}

function warning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

/**
 * Check if Supabase CLI is installed
 */
function checkSupabaseCLI() {
  try {
    execSync('npx supabase --version', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if user is authenticated with Supabase
 */
function checkSupabaseAuth() {
  try {
    const result = execSync('npx supabase projects list', { encoding: 'utf8', stdio: 'pipe' });
    // If we get a result without error, user is authenticated
    return true;
  } catch (err) {
    // Check if error is due to auth
    if (err.message && err.message.includes('not logged in')) {
      return false;
    }
    // Some other error occurred
    throw err;
  }
}

/**
 * Parse .env file
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};

  content.split('\n').forEach(line => {
    // Skip empty lines and comments
    if (!line.trim() || line.trim().startsWith('#')) return;

    // Remove inline comments
    const commentIndex = line.indexOf('#');
    const cleanLine = commentIndex > -1 ? line.substring(0, commentIndex) : line;

    const [key, ...valueParts] = cleanLine.split('=');
    if (key && valueParts.length > 0) {
      const value = valueParts.join('=').trim();
      // Remove quotes if present
      env[key.trim()] = value.replace(/^["']|["']$/g, '');
    }
  });

  return env;
}


/**
 * Deploy secrets to Supabase
 */
function deploySecrets(secrets, environment = 'prod') {
  const projectRef = secrets.SUPABASE_PROJECT_REF;
  if (!projectRef) {
    error(`SUPABASE_PROJECT_REF not found in your environment file for the "${environment}" environment.`);
    process.exit(1);
  }
  const secretsToSet = [];

  // Text LLM configuration
  if (secrets.TEXT_LLM_PROVIDER) {
    secretsToSet.push({
      name: 'TEXT_LLM_PROVIDER',
      value: secrets.TEXT_LLM_PROVIDER
    });
  }

  if (secrets.TEXT_API_KEY) {
    secretsToSet.push({
      name: 'TEXT_API_KEY',
      value: secrets.TEXT_API_KEY
    });
  }

  if (secrets.TEXT_MODEL) {
    secretsToSet.push({
      name: 'TEXT_MODEL',
      value: secrets.TEXT_MODEL
    });
  }

  if (secrets.TEXT_TEMPERATURE) {
    secretsToSet.push({
      name: 'TEXT_TEMPERATURE',
      value: secrets.TEXT_TEMPERATURE
    });
  }

  if (secrets.TEXT_MAX_OUTPUT_TOKENS) {
    secretsToSet.push({
      name: 'TEXT_MAX_OUTPUT_TOKENS',
      value: secrets.TEXT_MAX_OUTPUT_TOKENS
    });
  }

  // Image processing configuration
  if (secrets.IMAGE_LLM_PROVIDER) {
    secretsToSet.push({
      name: 'IMAGE_LLM_PROVIDER',
      value: secrets.IMAGE_LLM_PROVIDER
    });
  }

  if (secrets.IMAGE_MODEL) {
    secretsToSet.push({
      name: 'IMAGE_MODEL',
      value: secrets.IMAGE_MODEL
    });
  }

  if (secrets.IMAGE_API_KEY) {
    secretsToSet.push({
      name: 'IMAGE_API_KEY',
      value: secrets.IMAGE_API_KEY
    });
  }

  if (secrets.IMAGE_TEMPERATURE) {
    secretsToSet.push({
      name: 'IMAGE_TEMPERATURE',
      value: secrets.IMAGE_TEMPERATURE
    });
  }

  if (secrets.IMAGE_MAX_OUTPUT_TOKENS) {
    secretsToSet.push({
      name: 'IMAGE_MAX_OUTPUT_TOKENS',
      value: secrets.IMAGE_MAX_OUTPUT_TOKENS
    });
  }

  // Audio configuration
  if (secrets.AUDIO_LLM_PROVIDER) {
    secretsToSet.push({
      name: 'AUDIO_LLM_PROVIDER',
      value: secrets.AUDIO_LLM_PROVIDER
    });
  }

  if (secrets.AUDIO_API_KEY) {
    secretsToSet.push({
      name: 'AUDIO_API_KEY',
      value: secrets.AUDIO_API_KEY
    });
  }

  if (secrets.AUDIO_MODEL) {
    secretsToSet.push({
      name: 'AUDIO_MODEL',
      value: secrets.AUDIO_MODEL
    });
  }

  if (secrets.AUDIO_TEMPERATURE) {
    secretsToSet.push({
      name: 'AUDIO_TEMPERATURE',
      value: secrets.AUDIO_TEMPERATURE
    });
  }

  if (secrets.AUDIO_MAX_OUTPUT_TOKENS) {
    secretsToSet.push({
      name: 'AUDIO_MAX_OUTPUT_TOKENS',
      value: secrets.AUDIO_MAX_OUTPUT_TOKENS
    });
  }

  if (secrets.AUDIO_VOICE_ARABIC) {
    secretsToSet.push({
      name: 'AUDIO_VOICE_ARABIC',
      value: secrets.AUDIO_VOICE_ARABIC
    });
  }

  if (secrets.AUDIO_VOICE_ENGLISH) {
    secretsToSet.push({
      name: 'AUDIO_VOICE_ENGLISH',
      value: secrets.AUDIO_VOICE_ENGLISH
    });
  }

  if (secrets.AUDIO_INSTRUCTIONS_ARABIC) {
    secretsToSet.push({
      name: 'AUDIO_INSTRUCTIONS_ARABIC',
      value: secrets.AUDIO_INSTRUCTIONS_ARABIC
    });
  }

  if (secrets.AUDIO_INSTRUCTIONS_ENGLISH) {
    secretsToSet.push({
      name: 'AUDIO_INSTRUCTIONS_ENGLISH',
      value: secrets.AUDIO_INSTRUCTIONS_ENGLISH
    });
  }

  if (secretsToSet.length === 0) {
    warning('No LLM-related environment variables found in .env file');
    return false;
  }

  // Set each secret with project reference
  secretsToSet.forEach(({ name, value }) => {
    try {
      info(`Setting ${name} for ${environment} environment...`);
      // Escape special characters in value for shell command
      const escapedValue = value.replace(/"/g, '\\"').replace(/\$/g, '\\$');
      execSync(`npx supabase secrets set ${name}="${escapedValue}" --project-ref ${projectRef}`, { stdio: 'inherit' });
      success(`${name} set successfully for ${environment}`);
    } catch (err) {
      error(`Failed to set ${name}: ${err.message}`);
      throw err;
    }
  });

  return true;
}

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const environment = args.find(arg => arg.startsWith('--env='))?.split('=')[1] || 'prod';
  
  if (!['prod', 'staging', 'dev'].includes(environment)) {
    error(`Invalid environment: ${environment}. Must be 'prod', 'staging', or 'dev'`);
    process.exit(1);
  }
  
  return { environment };
}

/**
 * Main function
 */
async function main() {
  const { environment } = parseArgs();
  
  log(`\n🚀 Supabase Environment Deployment Script (${environment.toUpperCase()})\n`, colors.bright);

  // Step 1: Check Supabase CLI
  info('Checking Supabase CLI installation...');
  if (!checkSupabaseCLI()) {
    error('Supabase CLI is not available');
    console.log('\nTo install Supabase CLI as a dev dependency:');
    console.log('  npm install --save-dev supabase');
    console.log('\nThis project uses npx to run the Supabase CLI from the local node_modules');
    console.log('For more information: https://supabase.com/docs/guides/cli');
    process.exit(1);
  }
  success('Supabase CLI is installed');

  // Step 2: Check authentication
  info('Checking Supabase authentication...');
  try {
    if (!checkSupabaseAuth()) {
      error('Not authenticated with Supabase');
      console.log('\nTo authenticate:');
      console.log('  npx supabase login');
      console.log('\nYou will need your Supabase access token from:');
      console.log('  https://app.supabase.com/account/tokens');
      process.exit(1);
    }
  } catch (err) {
    error('Failed to check authentication status');
    console.log('\nPlease ensure you are authenticated:');
    console.log('  npx supabase login');
    process.exit(1);
  }
  success('Authenticated with Supabase');

  // Step 3: Parse environment-specific .env file
  info(`Reading ${environment} environment variables...`);
  const envFileName = environment === 'staging' ? '.env.staging' : 
                      environment === 'dev' ? '.env.development' : '.env.production';
  const envPath = path.join(process.cwd(), 'config', envFileName);
  
  if (!fs.existsSync(envPath)) {
    error(`${envFileName} file not found`);
    console.log(`\nPlease create a config/${envFileName} file with your environment variables.`);
    if (environment === 'staging') {
      console.log('You can copy config/.env.staging.example as a starting point:');
      console.log('  cp config/.env.staging.example config/.env.staging');
    } else if (environment === 'dev') {
      console.log('You can copy config/.env.development.example as a starting point:');
      console.log('  cp config/.env.development.example config/.env.development');
    } else {
      console.log('You can copy config/.env.production.example as a starting point:');
      console.log('  cp config/.env.production.example config/.env.production');
    }
    process.exit(1);
  }

  const envVars = parseEnvFile(envPath);
  const relevantVars = Object.keys(envVars).filter(key =>
    key.startsWith('TEXT_') || key === 'SUPABASE_PROJECT_REF' ||
    key.includes('IMAGE_') || key.includes('AUDIO_')
  );

  if (relevantVars.length === 0) {
    warning('No LLM-related variables found in .env file');
    console.log('\nText LLM configuration:');
    console.log('  TEXT_LLM_PROVIDER - Choose provider: "gemini" or "openrouter" (default: gemini)');
    console.log('  TEXT_API_KEY - Your API key for the selected provider (required)');
    console.log('  TEXT_MODEL - Model to use (provider-specific defaults)');
    console.log('  TEXT_TEMPERATURE - Temperature for generation (default: 0.3)');
    console.log('  TEXT_MAX_OUTPUT_TOKENS - Max output tokens (default: 4096)');
    console.log('\nDefault models by provider:');
    console.log('  Gemini: gemini-2.5-flash-preview-05-20');
    console.log('  OpenRouter: google/gemini-2.5-flash');
    console.log('\nImage processing variables:');
    console.log('  IMAGE_LLM_PROVIDER - Provider for image processing (default: openrouter)');
    console.log('  IMAGE_MODEL - Model for image processing (default: google/gemini-2.0-flash-exp:free)');
    console.log('  IMAGE_API_KEY - API key for image processing (required)');
    console.log('  IMAGE_TEMPERATURE - Temperature for image generation (default: 0.3)');
    console.log('  IMAGE_MAX_OUTPUT_TOKENS - Max output tokens for images (default: 4096)');
    process.exit(1);
  }

  success(`Found ${relevantVars.length} relevant environment variables`);

  // Step 4: Deploy secrets
  info(`Deploying secrets to Supabase ${environment} environment...`);
  try {
    const deployed = deploySecrets(envVars, environment);
    if (deployed) {
      success(`Environment variables deployed successfully to ${environment}!`);
      
      console.log('\n📝 Next steps:');
      if (environment === 'staging') {
        console.log('  1. Deploy your edge functions: npm run supabase:staging:functions');
        console.log('  2. Or deploy everything: npm run supabase:staging');
      } else if (environment === 'dev') {
        console.log('  1. Deploy your edge functions: npm run supabase:dev:functions');
        console.log('  2. Or deploy everything: npm run supabase:dev');
      } else {
        console.log('  1. Deploy your edge functions: npm run supabase:prod:functions');
        console.log('  2. Or deploy everything: npm run supabase:prod');
      }
    }
  } catch (err) {
    error('Failed to deploy secrets');
    console.log('\nPlease check your Supabase project configuration and try again.');
    process.exit(1);
  }
}

// Run the script
main().catch(err => {
  error(`Unexpected error: ${err.message}`);
  process.exit(1);
});
