
/* Primary Arabic font - Amiri */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile viewport and scrolling fixes - targeted approach */
@layer base {
  /* Mobile-specific fixes */
  @media (max-width: 768px) {
    body {
      /* Prevent pull-to-refresh and overscroll */
      overscroll-behavior-y: none;
      /* Prevent zoom on input focus */
      touch-action: manipulation;
    }
  }
}

/* Custom checkbox styling */
@layer components {
  /* Make checkbox checkmarks white */
  .checkbox-white-check[data-state="checked"] svg {
    color: white !important;
  }
}

/* Force placeholder to display LTR even in RTL context */
@layer utilities {
  .placeholder-ltr::placeholder {
    direction: ltr;
    text-align: left;
  }
}

/* Optimized chat interface animations */
@keyframes message-enter {
  from {
    opacity: 0;
    transform: translate3d(0, 8px, 0) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes message-enter-user {
  from {
    opacity: 0;
    transform: translate3d(12px, 8px, 0) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes message-enter-assistant {
  from {
    opacity: 0;
    transform: translate3d(-8px, 8px, 0) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Optimized animation utility classes */
.animate-message-enter {
  animation: message-enter 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation-fill-mode: both;
}

.animate-message-enter-user {
  animation: message-enter-user 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation-fill-mode: both;
}

.animate-message-enter-assistant {
  animation: message-enter-assistant 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation-fill-mode: both;
}

/* Legacy fade-in for other elements */
.animate-fade-in {
  animation: message-enter 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  animation-fill-mode: both;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

.animate-gentle-bounce {
  animation: gentle-bounce 2s ease-in-out infinite;
}

.animate-gradient {
  animation: gradient 3s ease infinite;
  background-size: 300% 300%;
}

.bg-300\% {
  background-size: 300% 300%;
}

/* Streaming message animation */
@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

/* Custom header animations */
@keyframes float {
  0%, 100% { 
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }

  33% { 
    transform: translateY(-8px) rotate(2deg);
    opacity: 0.8;
  }

  66% { 
    transform: translateY(-4px) rotate(-1deg);
    opacity: 0.4;
  }
}

@keyframes float-delayed {
  0%, 100% { 
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }

  25% { 
    transform: translateY(-6px) rotate(-2deg);
    opacity: 0.7;
  }

  75% { 
    transform: translateY(-3px) rotate(1deg);
    opacity: 0.3;
  }
}

@keyframes float-slow {
  0%, 100% { 
    transform: translateY(0) rotate(0deg);
    opacity: 0.5;
  }

  50% { 
    transform: translateY(-10px) rotate(3deg);
    opacity: 0.8;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
  animation-delay: 4s;
}

/* Smooth scrolling for chat containers */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Enhanced smooth scrolling with custom timing and scrollbar styling */
.enhanced-smooth-scroll {
  scroll-behavior: smooth;
  transition: all 0.2s cubic-bezier(0.23, 1, 0.32, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Ensure Radix ScrollArea viewport behaves correctly */
[data-radix-scroll-area-viewport] {
  flex: 1;
  min-height: 0; /* Important for flex children */
}

/* Custom scrollbar styling to match app design */
.enhanced-smooth-scroll::-webkit-scrollbar {
  width: 6px;
}

.enhanced-smooth-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.enhanced-smooth-scroll::-webkit-scrollbar-thumb {
  background: rgba(5, 150, 105, 0.3); /* emerald-600 with opacity */
  border-radius: 6px;
  transition: background 0.2s ease;
}

.enhanced-smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(5, 150, 105, 0.5); /* emerald-600 with higher opacity */
}

/* Dark mode scrollbar styling */
.dark .enhanced-smooth-scroll::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.4); /* slate-600 with opacity */
}

.dark .enhanced-smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.6); /* slate-600 with higher opacity */
}

/* Firefox scrollbar styling */
.enhanced-smooth-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(5, 150, 105, 0.3) transparent;
}

.dark .enhanced-smooth-scroll {
  scrollbar-color: rgba(71, 85, 105, 0.4) transparent;
}


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --sidebar-background: 224 71.4% 4.1%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 210 20% 98%;
    --sidebar-primary-foreground: 220.9 39.3% 11%;
    --sidebar-accent: 215 27.9% 16.9%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
    --sidebar-ring: 216 12.2% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Global 110% zoom effect - desktop only */
  @media (min-width: 768px) {
    #root {
      transform: scale(1.25);
      transform-origin: top left;
      width: 80%; /* Compensate for the 110% scale (100/110 = 90.91%) */
      height: 80%;
    }
  }
}

/* Arabic text styling for assistant responses only */
@layer utilities {
  .assistant-arabic-text {
    font-size: 130%;
    line-height: 2; /* equivalent to leading-loose */
    font-family: 'Amiri', 'Arial', sans-serif;
  }
}
