import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Textarea } from '@/shared/components/ui/textarea';
import { Input } from '@/shared/components/ui/input';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Send, Upload, Mic, Volume2, Copy, AlertCircle, RefreshCw, Loader2, Square, Trash2 } from 'lucide-react';
import { useToast } from '@/shared/hooks/use-toast';
import { useTTS } from '@/hooks/useTTS';
import { useOCR } from '@/hooks/useOCR';
import LoadingSpinner from '@/shared/components/LoadingSpinner';
import ResultCard from '@/shared/components/ResultCard';
import { textLLMService } from '@/services/TextLLMService';
import { TransformRequest } from '@/shared/types/api.types';
import { IMAGE_UPLOAD_CONFIG } from '@/shared/types';
import { UnifiedMessage, InputType } from './types/message.types';

interface UnifiedTransformFormProps {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  messages: UnifiedMessage[];
  setMessages: React.Dispatch<React.SetStateAction<UnifiedMessage[]>>;
}

const UnifiedTransformForm: React.FC<UnifiedTransformFormProps> = ({
  sourceLang,
  targetLang,
  messages,
  setMessages
}) => {
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { play, stop, isPlaying, isLoadingAudio, isPlayingAudio } = useTTS();
  const { processImage, isProcessing: isProcessingImage, stage: ocrStage, progress: ocrProgress, result: ocrResult, reset: resetOCR } = useOCR();
  
  // State to track scroll position and container height
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [containerHeight, setContainerHeight] = useState<number>(0);

  // Language preference state with localStorage persistence
  const [canReadEnglish, setCanReadEnglish] = useState(() => {
    const saved = localStorage.getItem('canReadEnglish');
    return saved ? JSON.parse(saved) : true; // Default to checked
  });
  
  const [canReadArabic, setCanReadArabic] = useState(() => {
    const saved = localStorage.getItem('canReadArabic');
    return saved ? JSON.parse(saved) : false; // Default to unchecked
  });

  // Persist checkbox states to localStorage
  const handleCanReadEnglishChange = (checked: boolean) => {
    setCanReadEnglish(checked);
    localStorage.setItem('canReadEnglish', JSON.stringify(checked));
  };

  const handleCanReadArabicChange = (checked: boolean) => {
    setCanReadArabic(checked);
    localStorage.setItem('canReadArabic', JSON.stringify(checked));
  };

  // Helper functions to determine which operations to process based on reading abilities
  const shouldProcessSourceTransliteration = useCallback(() => {
    // Default behavior: both or neither checked - always process
    if ((canReadEnglish && canReadArabic) || (!canReadEnglish && !canReadArabic)) {
      return true;
    }
    
    // Only English reading enabled
    if (canReadEnglish && !canReadArabic) {
      // In ar-en mode: show Arabic transliteration (source)
      // In en-ar mode: skip English transliteration (source)
      return sourceLang === 'ar';
    }
    
    // Only Arabic reading enabled
    if (!canReadEnglish && canReadArabic) {
      // In en-ar mode: show English transliteration (source)
      // In ar-en mode: skip Arabic transliteration (source)
      return sourceLang === 'en';
    }
    
    return true;
  }, [canReadEnglish, canReadArabic, sourceLang]);

  const shouldProcessTargetTransliteration = useCallback(() => {
    // Default behavior: both or neither checked - always process
    if ((canReadEnglish && canReadArabic) || (!canReadEnglish && !canReadArabic)) {
      return true;
    }
    
    // Only English reading enabled
    if (canReadEnglish && !canReadArabic) {
      // In ar-en mode: skip English transliteration (target)
      // In en-ar mode: show Arabic transliteration (target)
      return targetLang === 'ar';
    }
    
    // Only Arabic reading enabled
    if (!canReadEnglish && canReadArabic) {
      // In en-ar mode: skip Arabic transliteration (target)
      // In ar-en mode: show English transliteration (target)
      return targetLang === 'en';
    }
    
    return true;
  }, [canReadEnglish, canReadArabic, targetLang]);

  // Prevent document body scrolling on mobile to fix scroll race condition
  useEffect(() => {
    // Only apply on mobile devices
    const isMobile = window.innerWidth < 768;
    if (!isMobile) return;
    
    // Store original values to restore on unmount
    const originalOverflow = document.body.style.overflow;
    const originalOverscrollBehavior = document.body.style.overscrollBehavior;
    
    // Apply mobile-specific scroll prevention
    document.body.style.overflow = 'hidden';
    document.body.style.overscrollBehavior = 'none';
    
    // Cleanup function to restore original values
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.overscrollBehavior = originalOverscrollBehavior;
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Add initial welcome message if no messages exist
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: UnifiedMessage = {
        id: 'welcome',
        type: 'assistant',
        inputType: 'text',
        content: "Assalāmu 'alaykum! Welcome to SalahScribe. I'm here to help you translate between Arabic and English, with transliteration to help you pronounce everything correctly. You can type or paste text in either language, or upload an image containing Arabic or English text to get started.",
        timestamp: new Date(),
        sourceLang: sourceLang,
        targetLang: targetLang,
        transliterationStatus: { status: 'idle' },
        translationStatus: { status: 'idle' },
        postTransliterationStatus: { status: 'idle' }
      };
      setMessages([welcomeMessage]);
    }
  }, [messages.length, sourceLang, targetLang]);

  // Enhanced scroll utility that ensures we reach the very bottom (internal scroll only)
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        const { scrollHeight, clientHeight } = scrollContainer;
        const shouldScroll = scrollHeight > clientHeight;
        
        if (shouldScroll) {
          scrollContainer.scrollTo({
            top: scrollContainer.scrollHeight,
            behavior
          });
        }
      }
    }
  }, []);

  // Handle text submission
  const handleTextSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputText.trim() || isLoading) return;

    const userMessage: UnifiedMessage = {
      id: Date.now().toString(),
      type: 'user',
      inputType: 'text',
      content: inputText,
      timestamp: new Date(),
      sourceLang: sourceLang,
      targetLang: targetLang,
      transliterationStatus: { status: 'idle' },
      translationStatus: { status: 'idle' },
      postTransliterationStatus: { status: 'idle' }
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    const shouldProcessSource = shouldProcessSourceTransliteration();
    const shouldProcessTarget = shouldProcessTargetTransliteration();

    const assistantMessage: UnifiedMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      inputType: 'text',
      content: '',
      transliteration: '',
      translation: '',
      original: inputText,
      timestamp: new Date(),
      sourceLang: sourceLang,
      targetLang: targetLang,
      transliterationStatus: { status: shouldProcessSource ? 'loading' : 'idle' },
      translationStatus: { status: 'loading' },
      postTransliterationStatus: { status: shouldProcessTarget ? 'loading' : 'idle' }
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      const request: TransformRequest = {
        text: inputText,
        sourceLang,
        targetLang,
      };

      // Process translation and conditional transliterations
      if (shouldProcessSource) {
        // Process both transliteration and translation in parallel
        await textLLMService.getTransformParallel(
          { ...request, stream: true },
          {
            onTransliterationChunk: (data) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? { ...msg, transliteration: data.transliteration || msg.transliteration, transliterationStatus: { status: 'loading' } }
                  : msg
              ));
            },
            onTransliterationComplete: (data) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? {
                      ...msg,
                      transliteration: data.transliteration,
                      transliterationStatus: { status: 'success', data: data.transliteration }
                    }
                  : msg
              ));
            },
            onTransliterationError: (error) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? {
                      ...msg,
                      transliterationStatus: { status: 'error', error: error.message }
                    }
                  : msg
              ));
            },
            onTranslationChunk: (data) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? { ...msg, translation: data.translation || msg.translation, translationStatus: { status: 'loading' } }
                  : msg
              ));
            },
            onTranslationComplete: (data) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? {
                      ...msg,
                      translation: data.translation,
                      translationStatus: { status: 'success', data: data.translation }
                    }
                  : msg
              ));

              // Now, trigger the post-translation transliteration if needed
              if (shouldProcessTarget) {
                textLLMService.getPostTransliteration(
                  {
                    text: data.translation,
                    sourceLang: targetLang, // The new source is the old target
                    stream: true,
                  },
                  (chunk) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === assistantMessage.id
                        ? { ...msg, postTransliteration: chunk.transliteration || msg.postTransliteration, postTransliterationStatus: { status: 'loading' } }
                        : msg
                    ));
                  },
                  (complete) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === assistantMessage.id
                        ? {
                            ...msg,
                            postTransliteration: complete.transliteration,
                            postTransliterationStatus: { status: 'success', data: complete.transliteration }
                          }
                        : msg
                    ));
                  },
                  (error) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === assistantMessage.id
                        ? {
                            ...msg,
                            postTransliterationStatus: { status: 'error', error: error.message }
                          }
                        : msg
                    ));
                  }
                );
              }
            },
            onTranslationError: (error) => {
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessage.id
                  ? {
                      ...msg,
                      translationStatus: { status: 'error', error: error.message }
                    }
                  : msg
              ));
            }
          }
        );
      } else {
        // Process only translation when source transliteration is skipped
        await textLLMService.getTranslation(
          request,
          undefined,
          (data) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessage.id
                ? {
                    ...msg,
                    translation: data.translation,
                    translationStatus: { status: 'success', data: data.translation }
                  }
                : msg
            ));

            // Now, trigger the post-translation transliteration if needed
            if (shouldProcessTarget) {
              textLLMService.getPostTransliteration(
                {
                  text: data.translation,
                  sourceLang: targetLang, // The new source is the old target
                  stream: true,
                },
                (chunk) => {
                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? { ...msg, postTransliteration: chunk.transliteration || msg.postTransliteration, postTransliterationStatus: { status: 'loading' } }
                      : msg
                  ));
                },
                (complete) => {
                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? {
                          ...msg,
                          postTransliteration: complete.transliteration,
                          postTransliterationStatus: { status: 'success', data: complete.transliteration }
                        }
                      : msg
                  ));
                },
                (error) => {
                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? {
                          ...msg,
                          postTransliterationStatus: { status: 'error', error: error.message }
                        }
                      : msg
                  ));
                }
              );
            }
          },
          (error) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessage.id
                ? {
                    ...msg,
                    translationStatus: { status: 'error', error: error.message }
                  }
                : msg
            ));
          }
        );
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to process text',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [inputText, isLoading, sourceLang, targetLang, toast, setMessages]);

  // Process image with OCR
  const handleProcessImage = useCallback(async (file: File, messageId: string) => {
    const shouldProcessSource = shouldProcessSourceTransliteration();
    const shouldProcessTarget = shouldProcessTargetTransliteration();

    const assistantMessage: UnifiedMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      inputType: 'image',
      content: '',
      sourceLang: sourceLang,
      targetLang: targetLang,
      timestamp: new Date(),
      transliterationStatus: { status: shouldProcessSource ? 'loading' : 'idle' },
      translationStatus: { status: 'loading' },
      postTransliterationStatus: { status: shouldProcessTarget ? 'loading' : 'idle' },
      ocrStatus: { status: 'loading' },
      stage: 'uploading',
      progress: 0
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // Use existing OCR hook with callbacks to update our message
      await processImage(file, sourceLang, targetLang);
    } catch (error) {
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessage.id
          ? {
              ...msg,
              ocrStatus: { status: 'error', error: error instanceof Error ? error.message : 'Failed to process image' }
            }
          : msg
      ));
    }
  }, [sourceLang, targetLang, processImage, setMessages]);

  // Handle image selection
  const handleImageSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const fileType = file.type.toLowerCase();
    if (!IMAGE_UPLOAD_CONFIG.allowedMimeTypes.includes(fileType)) {
      toast({
        title: "Invalid file type",
        description: `Please upload an image file (${IMAGE_UPLOAD_CONFIG.allowedFormats.join(', ')})`,
        variant: "destructive"
      });
      return;
    }

    // Validate file size
    if (file.size > IMAGE_UPLOAD_CONFIG.maxSizeBytes) {
      toast({
        title: "File too large",
        description: `Please upload an image smaller than ${IMAGE_UPLOAD_CONFIG.maxSizeBytes / (1024 * 1024)}MB`,
        variant: "destructive"
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      
      // Add user message with image
      const userMessage: UnifiedMessage = {
        id: Date.now().toString(),
        type: 'user',
        inputType: 'image',
        content: 'Image uploaded',
        imageUrl,
        imageFile: file,
        timestamp: new Date(),
        sourceLang: sourceLang,
        targetLang: targetLang,
        transliterationStatus: { status: 'idle' },
        translationStatus: { status: 'idle' },
        postTransliterationStatus: { status: 'idle' }
      };

      setMessages(prev => [...prev, userMessage]);

      // Process the image
      handleProcessImage(file, userMessage.id);
    };

    reader.readAsDataURL(file);
    
    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [toast, setMessages, handleProcessImage]);

  // Update messages when OCR hook state changes
  React.useEffect(() => {
    if (!isProcessingImage && !ocrResult) return;

    setMessages(prev => {
      const lastImageAssistantIndex = prev.findLastIndex(msg => msg.type === 'assistant' && msg.inputType === 'image');
      if (lastImageAssistantIndex === -1) return prev;

      return prev.map((msg, index) => {
        if (index === lastImageAssistantIndex) {
          const shouldProcessSource = shouldProcessSourceTransliteration();
          const shouldProcessTarget = shouldProcessTargetTransliteration();
          
          const updatedMessage = {
            ...msg,
            stage: ocrStage,
            progress: ocrProgress,
            extractedText: ocrResult?.extractedText || msg.extractedText,
            transliteration: shouldProcessSource ? (ocrResult?.transliteration || msg.transliteration) : undefined,
            translation: ocrResult?.translation || msg.translation,
            original: ocrResult?.extractedText || msg.original,
          };

          if (ocrStage === 'done' || !isProcessingImage) {
            updatedMessage.ocrStatus = { status: 'success', data: ocrResult?.extractedText };
            
            // Set transliteration status based on reading abilities
            if (shouldProcessSource) {
              updatedMessage.transliterationStatus = { status: 'success', data: ocrResult?.transliteration };
            } else {
              updatedMessage.transliterationStatus = { status: 'idle' };
            }
            
            updatedMessage.translationStatus = { status: 'success', data: ocrResult?.translation };
            
            // Trigger post-translation transliteration if translation is available and needed
            if (ocrResult?.translation && !updatedMessage.postTransliteration && shouldProcessTarget) {
              updatedMessage.postTransliterationStatus = { status: 'loading' };
              
              // Trigger post-translation transliteration
              setTimeout(() => {
                textLLMService.getPostTransliteration(
                  {
                    text: ocrResult.translation,
                    sourceLang: targetLang, // The new source is the old target
                    stream: true,
                  },
                  (chunk) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === updatedMessage.id
                        ? { ...msg, postTransliteration: chunk.transliteration || msg.postTransliteration, postTransliterationStatus: { status: 'loading' } }
                        : msg
                    ));
                  },
                  (complete) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === updatedMessage.id
                        ? {
                            ...msg,
                            postTransliteration: complete.transliteration,
                            postTransliterationStatus: { status: 'success', data: complete.transliteration }
                          }
                        : msg
                    ));
                  },
                  (error) => {
                    setMessages(prev => prev.map(msg =>
                      msg.id === updatedMessage.id
                        ? {
                            ...msg,
                            postTransliterationStatus: { status: 'error', error: error.message }
                          }
                        : msg
                    ));
                  }
                );
              }, 100); // Small delay to ensure UI updates
            } else if (!shouldProcessTarget) {
              updatedMessage.postTransliterationStatus = { status: 'idle' };
              updatedMessage.postTransliteration = undefined; // Suppress post-transliteration display
            }
          }
          
          return updatedMessage;
        }
        return msg;
      });
    });
  }, [ocrStage, ocrProgress, ocrResult, isProcessingImage, setMessages]);

  // Add scroll event listener to track position
  useEffect(() => {
    if (!scrollAreaRef.current) return;
    
    const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
    if (!scrollContainer) return;
    
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const threshold = 50; // pixels from bottom
      const atBottom = scrollHeight - scrollTop - clientHeight < threshold;
      setIsAtBottom(atBottom);
    };
    
    scrollContainer.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial position
    
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, []);

  // Add ResizeObserver to monitor container height changes
  useEffect(() => {
    if (!scrollAreaRef.current) return;
    
    const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
    if (!scrollContainer) return;
    
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { height } = entry.contentRect;
        setContainerHeight(height);
        
        // Remove auto-scroll from ResizeObserver to prevent conflicts
        // The main auto-scroll effect handles new content scrolling
      }
    });
    
    resizeObserver.observe(scrollContainer);
    
    return () => resizeObserver.disconnect();
  }, []);

  // Robust auto-scroll effect
  useEffect(() => {
    if (messages.length > 0) {
      // Don't auto-scroll on mobile for the initial welcome message to prevent page scroll
      const isMobile = window.innerWidth < 768; // md breakpoint
      const isOnlyWelcomeMessage = messages.length === 1 && messages[0].id === 'welcome';
      
      if (isMobile && isOnlyWelcomeMessage) {
        return; // Skip auto-scroll for welcome message on mobile
      }
      
      // Check if we should auto-scroll
      const lastMessage = messages[messages.length - 1];
      const isNewMessage = lastMessage &&
        (Date.now() - new Date(lastMessage.timestamp).getTime() < 1000);
      
      if (isNewMessage || isAtBottom) {
        // Use a small delay to ensure DOM has updated
        const timeoutId = setTimeout(() => {
          scrollToBottom('smooth');
        }, 100);
        
        return () => clearTimeout(timeoutId);
      }
    }
  }, [messages, scrollToBottom, isAtBottom]);

  // Force scroll after OCR completion
  useEffect(() => {
    if (ocrResult && !isProcessingImage) {
      const timeoutId = setTimeout(() => {
        scrollToBottom('smooth');
      }, 200);
      
      return () => clearTimeout(timeoutId);
    }
  }, [ocrResult, isProcessingImage, scrollToBottom]);

  // Retry functions
  const retryTransliteration = useCallback(async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.original) return;

    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, transliterationStatus: { status: 'loading' } }
        : msg
    ));

    try {
      await textLLMService.getTransliteration(
        {
          text: message.original,
          sourceLang: sourceLang,
        },
        undefined,
        (data) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  transliteration: data.transliteration,
                  transliterationStatus: { status: 'success', data: data.transliteration }
                }
              : msg
          ));
        },
        (error) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  transliterationStatus: { status: 'error', error: error.message }
                }
              : msg
          ));
          toast({
            title: 'Error',
            description: 'Failed to retry transliteration',
            variant: 'destructive',
          });
        }
      );
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to retry transliteration',
        variant: 'destructive',
      });
    }
  }, [messages, sourceLang, toast, setMessages]);

  const retryTranslation = useCallback(async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.original) return;

    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, translationStatus: { status: 'loading' } }
        : msg
    ));

    try {
      await textLLMService.getTranslation(
        {
          text: message.original,
          sourceLang: sourceLang,
          targetLang: targetLang,
        },
        undefined,
        (data) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  translation: data.translation,
                  translationStatus: { status: 'success', data: data.translation }
                }
              : msg
          ));
        },
        (error) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  translationStatus: { status: 'error', error: error.message }
                }
              : msg
          ));
          toast({
            title: 'Error',
            description: 'Failed to retry translation',
            variant: 'destructive',
          });
        }
      );
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to retry translation',
        variant: 'destructive',
      });
    }
  }, [messages, sourceLang, targetLang, toast, setMessages]);

  const retryPostTransliteration = useCallback(async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.translation) return;

    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, postTransliterationStatus: { status: 'loading' } }
        : msg
    ));

    try {
      await textLLMService.getPostTransliteration(
        {
          text: message.translation,
          sourceLang: targetLang,
        },
        undefined,
        (data) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  postTransliteration: data.transliteration,
                  postTransliterationStatus: { status: 'success', data: data.transliteration }
                }
              : msg
          ));
        },
        (error) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  postTransliterationStatus: { status: 'error', error: error.message }
                }
              : msg
          ));
          toast({
            title: 'Error',
            description: 'Failed to retry post-translation transliteration',
            variant: 'destructive',
          });
        }
      );
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to retry post-translation transliteration',
        variant: 'destructive',
      });
    }
  }, [messages, targetLang, toast, setMessages]);

  // Audio playback
  const handlePlayAudio = useCallback(async (text: string, lang: 'ar' | 'en') => {
    try {
      await play(text, lang);
    } catch (error) {
      toast({
        title: 'Playback Error',
        description: 'Failed to play audio',
        variant: 'destructive',
      });
    }
  }, [play, toast]);

  // Handle play/stop toggle
  const handleAudioToggle = useCallback((text: string, lang: 'ar' | 'en') => {
    if (isPlayingAudio(text, lang)) {
      stop();
    } else {
      handlePlayAudio(text, lang);
    }
  }, [isPlayingAudio, stop, handlePlayAudio]);

  // Copy to clipboard
  const copyToClipboard = useCallback((text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied!',
      description: `${label} copied to clipboard`,
    });
  }, [toast]);

  // Trigger image input
  const triggerImageInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Clear all messages
  const clearChat = useCallback(() => {
    setMessages([]);
    // Reset OCR state
    resetOCR();
    toast({
      title: 'Chat cleared',
      description: 'All messages have been removed',
    });
  }, [setMessages, resetOCR, toast]);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Messages */}
      <ScrollArea
        ref={scrollAreaRef}
        className="flex-1 min-h-0 enhanced-smooth-scroll bg-slate-50 dark:bg-slate-800"
      >
        <div className="space-y-3 p-3 sm:p-4">
          {messages.map((message, index) => {
            // Only animate the latest message to avoid choppy stacking
            const isLatest = index === messages.length - 1;
            const animationClass = isLatest 
              ? (message.type === 'user' ? 'animate-message-enter-user' : 'animate-message-enter-assistant')
              : ''; // No animation for older messages
            
            return (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} ${animationClass}`}
              >
              {message.type === 'user' ? (
                <div className="max-w-[85%] p-2 sm:p-3 rounded-xl shadow-sm bg-gradient-to-r from-emerald-600 to-emerald-700 dark:from-slate-600 dark:to-slate-700 text-white dark:text-slate-200 transition-all duration-200 hover:shadow-md">
                  {message.inputType === 'image' && message.imageUrl && (
                    <div className="relative mb-2 overflow-hidden rounded-lg bg-white/10">
                      <img
                        src={message.imageUrl}
                        alt="Uploaded image"
                        className="max-h-48 w-full object-contain"
                      />
                    </div>
                  )}
                  <div className="whitespace-pre-wrap text-right text-sm" dir={message.sourceLang === 'ar' ? 'rtl' : 'ltr'}>
                    {message.content}
                  </div>
                </div>
              ) : (
                <div className="max-w-[85%]">
                  {/* Loading state for image processing */}
                  {message.inputType === 'image' && message.ocrStatus?.status === 'loading' && (
                    <div className="bg-slate-100 dark:bg-slate-700 rounded-xl p-4 shadow-sm animate-pulse-subtle">
                      <LoadingSpinner size="sm" text={`${message.stage === 'uploading' ? 'Uploading' : message.stage === 'ocr' ? 'Extracting text' : 'Processing'}...`} />
                      <div className="mt-2 overflow-hidden rounded-full">
                        <div className="w-full bg-gray-200 dark:bg-gray-600 h-1.5">
                          <div
                            className="bg-emerald-600 dark:bg-emerald-500 h-1.5 rounded-full transition-all duration-700 ease-out"
                            style={{ width: `${message.progress || 0}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Basic message content for welcome messages */}
                  {message.content && !message.extractedText && !message.transliteration && !message.translation &&
                    message.transliterationStatus.status === 'idle' && message.translationStatus.status === 'idle' && (
                    <div className="bg-slate-100 dark:bg-slate-700 rounded-xl shadow-sm p-2 sm:p-3 transition-all duration-300 hover:shadow-md">
                      <div className="text-sm">
                        {message.content}
                      </div>
                    </div>
                  )}
                  {/* Unified result bubble */}
                  {(message.extractedText || message.transliteration || message.translation ||
                    (message.transliterationStatus.status === 'loading' || message.translationStatus.status === 'loading') ||
                    (message.transliterationStatus.status === 'error' || message.translationStatus.status === 'error')) && (
                    <div className="bg-slate-100 dark:bg-slate-700 rounded-xl shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md">
                      {/* Extracted text for images */}
                      {message.inputType === 'image' && message.extractedText && (
                        <div className="p-2 sm:p-3 border-b border-slate-200 dark:border-slate-600">
                          <div className="flex items-start justify-between gap-2 mb-1">
                            <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                              Extracted {message.sourceLang === 'ar' ? 'Arabic' : 'English'}
                            </span>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                onClick={() => handleAudioToggle(message.extractedText!, message.sourceLang)}
                              >
                                {isLoadingAudio(message.extractedText!, message.sourceLang) ? (
                                  <Loader2 className="w-3 h-3 animate-spin" />
                                ) : isPlayingAudio(message.extractedText!, message.sourceLang) ? (
                                  <Square className="w-3 h-3 transition-transform md:hover:scale-110" />
                                ) : (
                                  <Volume2 className="w-3 h-3 transition-transform md:hover:scale-110" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                onClick={() => copyToClipboard(message.extractedText!, "Extracted text")}
                              >
                                <Copy className="w-3 h-3 transition-transform md:hover:scale-110" />
                              </Button>
                            </div>
                          </div>
                          <div
                            className={`text-sm ${message.sourceLang === 'ar' ? 'assistant-arabic-text' : ''}`}
                            dir={message.sourceLang === 'ar' ? 'rtl' : 'ltr'}
                          >
                            {message.extractedText}
                          </div>
                        </div>
                      )}

                      {/* Transliteration */}
                      {(message.transliterationStatus.status !== 'idle' || message.transliteration) && (
                        <div className={`p-2 sm:p-3 ${message.translation || message.translationStatus.status !== 'idle' ? 'border-b border-slate-200 dark:border-slate-600' : ''}`}>
                          <div className="flex items-start justify-between gap-2 mb-1">
                            <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                              Original Transliteration
                            </span>
                            <div className="flex gap-1">
                              {message.transliterationStatus.status === 'error' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                  onClick={() => retryTransliteration(message.id)}
                                >
                                  <RefreshCw className="w-3 h-3 transition-transform md:hover:rotate-180" />
                                </Button>
                              )}
                              {message.transliterationStatus.status === 'success' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => handleAudioToggle(
                                      message.original || message.transliteration!,
                                      message.sourceLang
                                    )}
                                  >
                                    {isLoadingAudio(message.original || message.transliteration!, message.sourceLang) ? (
                                      <Loader2 className="w-3 h-3 animate-spin" />
                                    ) : isPlayingAudio(message.original || message.transliteration!, message.sourceLang) ? (
                                      <Square className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    ) : (
                                      <Volume2 className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    )}
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => copyToClipboard(message.transliteration!, 'Transliteration')}
                                  >
                                    <Copy className="w-3 h-3 transition-transform md:hover:scale-110" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                          {message.transliterationStatus.status === 'loading' ? (
                            message.transliteration ? (
                              <div className={`text-sm ${message.sourceLang === 'en' ? 'assistant-arabic-text' : ''}`}>{message.transliteration}</div>
                            ) : (
                              <div className="flex items-center gap-2 text-xs text-slate-600 dark:text-slate-400">
                                <LoadingSpinner size="xs" className="animate-pulse-subtle" />
                                <span>Transliterating...</span>
                              </div>
                            )
                          ) : message.transliterationStatus.status === 'error' ? (
                            <div className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400">
                              <AlertCircle className="w-3 h-3" />
                              <span>{message.transliterationStatus.error || 'Failed'}</span>
                            </div>
                          ) : (
                            <div className={`text-sm ${message.sourceLang === 'en' ? 'assistant-arabic-text' : ''}`} dir={message.sourceLang === 'ar' ? 'ltr' : 'rtl'}>{message.transliteration}</div>
                          )}
                        </div>
                      )}

                      {/* Translation */}
                      {(message.translationStatus.status !== 'idle' || message.translation) && (
                        <div className="p-2 sm:p-3">
                          <div className="flex items-start justify-between gap-2 mb-1">
                            <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                              {message.sourceLang === 'ar' ? 'English' : 'Arabic'} Translation
                            </span>
                            <div className="flex gap-1">
                              {message.translationStatus.status === 'error' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                  onClick={() => retryTranslation(message.id)}
                                >
                                  <RefreshCw className="w-3 h-3 transition-transform md:hover:rotate-180" />
                                </Button>
                              )}
                              {message.translationStatus.status === 'success' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => handleAudioToggle(message.translation!, message.sourceLang === 'ar' ? 'en' : 'ar')}
                                  >
                                    {isLoadingAudio(message.translation!, message.sourceLang === 'ar' ? 'en' : 'ar') ? (
                                      <Loader2 className="w-3 h-3 animate-spin" />
                                    ) : isPlayingAudio(message.translation!, message.sourceLang === 'ar' ? 'en' : 'ar') ? (
                                      <Square className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    ) : (
                                      <Volume2 className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    )}
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => copyToClipboard(message.translation!, 'Translation')}
                                  >
                                    <Copy className="w-3 h-3 transition-transform md:hover:scale-110" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                          {message.translationStatus.status === 'loading' ? (
                            message.translation ? (
                              <div className={`text-sm ${message.targetLang === 'ar' ? 'assistant-arabic-text' : ''}`} dir={message.targetLang === 'ar' ? 'rtl' : 'ltr'}>{message.translation}</div>
                            ) : (
                              <div className="flex items-center gap-2 text-xs text-slate-600 dark:text-slate-400">
                                <LoadingSpinner size="xs" className="animate-pulse-subtle" />
                                <span>Translating...</span>
                              </div>
                            )
                          ) : message.translationStatus.status === 'error' ? (
                            <div className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400">
                              <AlertCircle className="w-3 h-3" />
                              <span>{message.translationStatus.error || 'Failed'}</span>
                            </div>
                          ) : (
                            <div className={`text-sm ${message.targetLang === 'ar' ? 'assistant-arabic-text' : ''}`} dir={message.targetLang === 'ar' ? 'rtl' : 'ltr'}>{message.translation}</div>
                          )}
                        </div>
                      )}

                      {/* Post-Translation Transliteration */}
                      {(message.postTransliterationStatus.status !== 'idle' || message.postTransliteration) && (
                        <div className="p-2 sm:p-3 border-t border-slate-200 dark:border-slate-600">
                          <div className="flex items-start justify-between gap-2 mb-1">
                            <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                              {message.targetLang === 'ar' ? 'Arabic' : 'English'} Transliteration
                            </span>
                            <div className="flex gap-1">
                              {message.postTransliterationStatus.status === 'error' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                  onClick={() => retryPostTransliteration(message.id)}
                                >
                                  <RefreshCw className="w-3 h-3 transition-transform md:hover:rotate-180" />
                                </Button>
                              )}
                              {message.postTransliterationStatus.status === 'success' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => handleAudioToggle(
                                      message.translation ? message.translation : message.postTransliteration!,
                                      message.sourceLang === 'ar' ? 'en' : 'ar'
                                    )}
                                  >
                                    {isLoadingAudio(
                                      message.translation ? message.translation : message.postTransliteration!,
                                      message.sourceLang === 'ar' ? 'en' : 'ar'
                                    ) ? (
                                      <Loader2 className="w-3 h-3 animate-spin" />
                                    ) : isPlayingAudio(
                                      message.translation ? message.translation : message.postTransliteration!,
                                      message.sourceLang === 'ar' ? 'en' : 'ar'
                                    ) ? (
                                      <Square className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    ) : (
                                      <Volume2 className="w-3 h-3 transition-transform md:hover:scale-110" />
                                    )}
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 md:hover:bg-slate-200 md:dark:hover:bg-slate-600 rounded transition-colors [-webkit-tap-highlight-color:transparent] focus:bg-transparent"
                                    onClick={() => copyToClipboard(message.postTransliteration!, 'Post-translation transliteration')}
                                  >
                                    <Copy className="w-3 h-3 transition-transform md:hover:scale-110" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                          {message.postTransliterationStatus.status === 'loading' ? (
                            message.postTransliteration ? (
                              <div className={`text-sm ${message.sourceLang === 'ar' ? 'assistant-arabic-text' : ''}`} dir={message.targetLang === 'ar' ? 'rtl' : 'ltr'}>{message.postTransliteration}</div>
                            ) : (
                              <div className="flex items-center gap-2 text-xs text-slate-600 dark:text-slate-400">
                                <LoadingSpinner size="xs" className="animate-pulse-subtle" />
                                <span>Transliterating...</span>
                              </div>
                            )
                          ) : message.postTransliterationStatus.status === 'error' ? (
                            <div className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400">
                              <AlertCircle className="w-3 h-3" />
                              <span>{message.postTransliterationStatus.error || 'Failed'}</span>
                            </div>
                          ) : (
                            <div className={`text-sm ${message.sourceLang === 'ar' ? 'assistant-arabic-text' : ''}`} dir={message.targetLang === 'ar' ? 'ltr' : 'rtl'}>{message.postTransliteration}</div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
            );
          })}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>

      {/* Input Form */}
      <form onSubmit={handleTextSubmit} className="flex-shrink-0 p-3 border-t border-emerald-200/50 dark:border-slate-600/50 bg-slate-50/50 dark:bg-slate-800/50 animate-fade-in">
        <div className="relative">
          <Textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleTextSubmit(e);
              }
            }}
            placeholder={`Enter ${sourceLang === 'ar' ? 'Arabic' : 'English'} text here...`}
            className="w-full min-h-[60px] resize-none border-2 border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-emerald-500 dark:focus:ring-slate-500 focus:border-transparent bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-sm transition-all duration-200 focus:shadow-lg pb-14 pr-4 backdrop-blur-sm text-left"
            rows={1}
            disabled={isLoading || isProcessingImage}
            dir={inputText && sourceLang === 'ar' ? 'rtl' : 'ltr'}
          />
          
          {/* Button group in bottom right of textarea */}
          <div className="absolute bottom-2 right-2 flex items-center gap-1">
            {/* Send text button */}
            <Button
              type="submit"
              disabled={!inputText.trim() || isLoading || isProcessingImage}
              className="h-9 w-9 p-0 flex items-center justify-center bg-gradient-to-r from-emerald-600 to-emerald-700 md:hover:from-emerald-700 md:hover:to-emerald-800 dark:from-slate-700 dark:to-slate-800 md:dark:hover:from-slate-600 md:dark:hover:to-slate-700 text-white rounded-lg shadow-md md:hover:shadow-lg transition-all duration-200 disabled:opacity-50 md:hover:scale-110 [-webkit-tap-highlight-color:transparent] group"
              title="Send text"
            >
              <Send className="w-4 h-4 transition-transform md:group-hover:translate-x-0.5" />
            </Button>
            
            {/* Separator */}
            <div className="w-px h-6 bg-slate-300 dark:bg-slate-600 mx-1" />
            
            {/* Upload button */}
            <Button
              type="button"
              onClick={triggerImageInput}
              disabled={isLoading || isProcessingImage}
              className="h-9 w-9 p-0 flex items-center justify-center bg-gradient-to-r from-blue-600 to-blue-700 md:hover:from-blue-700 md:hover:to-blue-800 dark:from-slate-700 dark:to-slate-800 md:dark:hover:from-slate-600 md:dark:hover:to-slate-700 text-white rounded-lg shadow-md md:hover:shadow-lg transition-all duration-200 disabled:opacity-50 md:hover:scale-110 [-webkit-tap-highlight-color:transparent]"
              title="Upload file"
            >
              <Upload className="w-4 h-4" />
            </Button>
            
            {/* Live audio button (disabled) */}
            <Button
              type="button"
              disabled
              className="h-9 w-9 p-0 flex items-center justify-center bg-gray-400 dark:bg-gray-600 text-white/70 rounded-lg shadow-md opacity-50 cursor-not-allowed transition-all duration-200"
              title="Live audio (coming soon)"
            >
              <Mic className="w-4 h-4 opacity-70" />
            </Button>
            
            {/* Clear Chat button */}
            {(
              <>
                {/* Separator */}
                <div className="w-px h-6 bg-slate-300 dark:bg-slate-600 mx-1" />
                
                <Button
                  type="button"
                  onClick={clearChat}
                  disabled={messages.length <= 1}
                  className="h-9 w-9 p-0 flex items-center justify-center bg-gradient-to-r from-emerald-600 to-emerald-700 md:hover:from-emerald-700 md:hover:to-emerald-800 dark:from-slate-700 dark:to-slate-800 md:dark:hover:from-slate-600 md:dark:hover:to-slate-700 text-white rounded-lg shadow-md md:hover:shadow-lg transition-all duration-200 disabled:opacity-50 md:hover:scale-110 [-webkit-tap-highlight-color:transparent]"
                  title="Clear chat"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </>
            )}
          </div>
        </div>
        
        {/* Hidden file input */}
        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageSelect}
          className="hidden"
        />
        
        <div className="mt-2 sm:mt-3">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="can-read-english"
                checked={canReadEnglish}
                onCheckedChange={handleCanReadEnglishChange}
                className="checkbox-white-check data-[state=checked]:bg-emerald-400 data-[state=checked]:border-emerald-400 border-emerald-300 dark:border-slate-500 dark:data-[state=checked]:bg-slate-500 dark:data-[state=checked]:border-slate-500"
              />
              <label
                htmlFor="can-read-english"
                className="text-[11px] sm:text-xs text-emerald-600/80 dark:text-slate-400 cursor-pointer md:hover:text-emerald-700 md:dark:hover:text-slate-300 transition-colors relative top-[2px] sm:top-0 [-webkit-tap-highlight-color:transparent]"
              >
                I can read English
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <label
                htmlFor="can-read-arabic"
                className="text-[11px] sm:text-xs text-emerald-600/80 dark:text-slate-400 cursor-pointer md:hover:text-emerald-700 md:dark:hover:text-slate-300 transition-colors relative top-[2px] sm:top-0 [-webkit-tap-highlight-color:transparent]"
              >
                أنا أستطيع قراءة اللغة العربية
              </label>
              <Checkbox
                id="can-read-arabic"
                checked={canReadArabic}
                onCheckedChange={handleCanReadArabicChange}
                className="checkbox-white-check data-[state=checked]:bg-emerald-400 data-[state=checked]:border-emerald-400 border-emerald-300 dark:border-slate-500 dark:data-[state=checked]:bg-slate-500 dark:data-[state=checked]:border-slate-500"
              />
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default UnifiedTransformForm;