export type InputType = 'text' | 'image' | 'audio';
export type MessageType = 'user' | 'assistant';

export interface OperationStatus<T> {
  status: 'idle' | 'loading' | 'success' | 'error';
  data?: T;
  error?: string;
}

export interface UnifiedMessage {
  id: string;
  type: MessageType;
  inputType: InputType;
  content: string;
  imageUrl?: string; // For image messages
  imageFile?: File; // Store the file for processing
  timestamp: Date;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  
  // Results
  extractedText?: string; // For OCR results
  transliteration?: string;
  translation?: string;
  original?: string; // Store the original text for audio playback
  postTransliteration?: string;
  
  // Status tracking
  ocrStatus?: OperationStatus<string>;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
  postTransliterationStatus: OperationStatus<string>;
  
  // Progress tracking for image processing
  stage?: 'uploading' | 'ocr' | 'transliterating' | 'translating' | 'done';
  progress?: number;
}

export interface TransformCallbacks {
  onTransliterationComplete: (transliteration: string) => void;
  onTransliterationError: (error: Error) => void;
  onTranslationComplete: (translation: string) => void;
  onTranslationError: (error: Error) => void;
}

export interface OCRCallbacks extends TransformCallbacks {
  onOCRComplete: (extractedText: string) => void;
  onOCRError: (error: Error) => void;
  onStageChange: (stage: UnifiedMessage['stage'], progress: number) => void;
}