import { supabase } from '@/integrations/supabase/client';

export interface AudioRequest {
  text: string;
  lang: 'ar' | 'en';
}

export class AudioTTSService {
  private audioCache = new Map<string, string>();
  private currentAudio: HTMLAudioElement | null = null;

  /**
   * Generate audio from text using GPT-4o Mini
   * Returns a blob URL that can be played in an audio element
   */
  async generateAudio(request: AudioRequest): Promise<string> {
    // Create a cache key from the request
    const cacheKey = `${request.lang}:${request.text}`;
    
    // Check cache first
    if (this.audioCache.has(cacheKey)) {
      return this.audioCache.get(cacheKey)!;
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      // Use session token if available, otherwise use anon key
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY;

      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/audio`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
          body: JSON.stringify(request),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Check if we got a valid response
      const contentType = response.headers.get('content-type');
      console.log('Audio response content-type:', contentType);
      
      // Get the audio data as an ArrayBuffer
      const audioData = await response.arrayBuffer();
      console.log('Audio data size:', audioData.byteLength, 'bytes');
      
      if (audioData.byteLength === 0) {
        throw new Error('Received empty audio data from server');
      }
      
      // Create a blob from the audio data
      const blob = new Blob([audioData], { type: contentType || 'audio/mp3' });
      
      // Create a blob URL
      const blobUrl = URL.createObjectURL(blob);
      console.log('Created blob URL:', blobUrl);
      
      // Cache the blob URL
      this.audioCache.set(cacheKey, blobUrl);
      
      return blobUrl;
    } catch (error) {
      console.error('Audio generation failed:', error);
      throw error;
    }
  }

  /**
   * Play audio from a blob URL
   */
  async play(blobUrl: string): Promise<void> {
    console.log('Attempting to play audio from:', blobUrl);
    
    // Stop any currently playing audio
    this.stop();
    
    const audio = new Audio(blobUrl);
    this.currentAudio = audio;
    
    return new Promise((resolve, reject) => {
      audio.addEventListener('ended', () => {
        console.log('Audio playback ended successfully');
        this.currentAudio = null;
        resolve();
      });
      
      audio.addEventListener('error', (e) => {
        console.error('Audio element error:', e);
        this.currentAudio = null;
        const audioError = e.target as HTMLAudioElement;
        let errorMessage = 'Audio playback failed';
        
        if (audioError.error) {
          // MediaError codes
          switch (audioError.error.code) {
            case 1:
              errorMessage = 'Audio loading aborted';
              break;
            case 2:
              errorMessage = 'Network error while loading audio';
              break;
            case 3:
              errorMessage = 'Audio decoding error';
              break;
            case 4:
              errorMessage = 'Audio format not supported';
              break;
          }
        }
        
        reject(new Error(errorMessage));
      });
      
      audio.play()
        .then(() => console.log('Audio playback started'))
        .catch((err) => {
          console.error('Failed to start audio playback:', err);
          this.currentAudio = null;
          reject(err);
        });
    });
  }

  /**
   * Stop any currently playing audio
   */
  stop(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
  }

  /**
   * Clean up cached blob URLs to prevent memory leaks
   */
  clearCache(): void {
    this.audioCache.forEach(blobUrl => {
      URL.revokeObjectURL(blobUrl);
    });
    this.audioCache.clear();
  }
}

// Export a singleton instance
export const audioTTSService = new AudioTTSService();

// Clean up on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    audioTTSService.clearCache();
  });
}