import { supabase } from '@/integrations/supabase/client';
import type {
  TransliterationRequest,
  TransliterationResponse,
  ApiResponse,
  ImageTransliterationRequest,
  ImageTransliterationResponse,
  ImageTransliterationStage,
} from '@/shared/types';

export type ImageTransliterationEvent =
  | { type: 'stage'; stage: ImageTransliterationStage; arabicText?: string }
  | { type: 'complete'; response: ImageTransliterationResponse }
  | { type: 'error'; error: Error };

export type AudioTransliterationEvent =
  | { type: 'stage'; stage: 'processing' | 'transcription' | 'transliteration'; arabicText?: string }
  | { type: 'complete'; response: AudioTransliterationResponse }
  | { type: 'error'; error: Error };

interface TransliterationFunctionResponse {
  transliteration: string;
  confidence?: number;
  alternatives?: string[];
}

export interface AudioTransliterationResponse {
  arabicText: string;
  transliteration: string;
  translation: string;
  audioUrl?: string;
}

export const transliterateText = async (
  request: TransliterationRequest
): Promise<ApiResponse<TransliterationResponse>> => {
  try {
    const { data, error } = await supabase.functions.invoke<TransliterationFunctionResponse>('transliterate', {
      body: request
    });

    if (error) {
      return {
        error: {
          message: error.message || 'Failed to transliterate text',
          code: 'TRANSLITERATION_ERROR'
        }
      };
    }

    if (!data?.transliteration) {
      return {
        error: {
          message: 'Invalid response from transliteration service',
          code: 'INVALID_RESPONSE'
        }
      };
    }

    // Save transliteration to database if user is logged in
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (!userError && user) {
      const { error: insertError } = await supabase
        .from('transliterations')
        .insert({
          user_id: user.id,
          original_text: request.text,
          transliterated_text: data.transliteration
        });
      
      // Log insert error but don't fail the transliteration
      if (insertError) {
        // Could implement proper logging here instead of console.error
      }
    }

    const response: TransliterationResponse = {
      transliterated_text: data.transliteration,
      original_text: request.text,
      from_script: request.from_script || 'auto',
      to_script: request.to_script || 'latin',
      ...(data.confidence !== undefined && { confidence: data.confidence }),
      ...(data.alternatives !== undefined && { alternatives: data.alternatives })
    };

    return { data: response };
  } catch (error) {
    return {
      error: {
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

// Streaming transliteration function
export const transliterateTextStream = async (
  request: TransliterationRequest & { stream: true },
  onChunk: (chunk: string) => void,
  onComplete: () => void,
  onError: (error: Error) => void
): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    // Get the auth token for the request
    const { data: { session } } = await supabase.auth.getSession();
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    const authToken = session?.access_token || anonKey;

    // Construct the URL for the edge function
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const functionUrl = `${supabaseUrl}/functions/v1/transliterate`;

    const headers: Record<string, string> = { 'Content-Type': 'application/json' };
    headers['Authorization'] = `Bearer ${authToken}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ ...request, stream: true }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';
    let fullText = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Append new chunk to buffer
        buffer += decoder.decode(value, { stream: true });

        // Process complete lines from buffer
        while (true) {
          const lineEnd = buffer.indexOf('\n');
          if (lineEnd === -1) break;

          const line = buffer.slice(0, lineEnd).trim();
          buffer = buffer.slice(lineEnd + 1);

          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              // Save to database if user is logged in
              if (user && fullText) {
                await supabase
                  .from('transliterations')
                  .insert({
                    user_id: user.id,
                    original_text: request.text,
                    transliterated_text: fullText
                  });
              }
              onComplete();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.text) {
                fullText += parsed.text;
                onChunk(parsed.text);
              }
            } catch (e) {
              // Ignore invalid JSON
            }
          }
        }
      }
    } finally {
      reader.cancel();
    }
  } catch (error) {
    onError(error instanceof Error ? error : new Error('An unexpected error occurred'));
  }
};

// Image transliteration function
export const transliterateImage = async (
  imageData: File | string,
  onComplete: (response: ImageTransliterationResponse) => void,
  onError: (error: Error) => void
): Promise<void> => {
  try {
    let base64Image: string;
    if (imageData instanceof File) {
      const reader = new FileReader();
      base64Image = await new Promise<string>((resolve, reject) => {
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(imageData);
      });
    } else {
      base64Image = imageData;
    }

    const { data: { session } } = await supabase.auth.getSession();
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    const authToken = session?.access_token || anonKey;
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const functionUrl = `${supabaseUrl}/functions/v1/transliterate-image`;

    const headers: Record<string, string> = { 'Content-Type': 'application/json' };
    headers['Authorization'] = `Bearer ${authToken}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ imageData: base64Image }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const finalResponse: ImageTransliterationResponse = await response.json();
    onComplete(finalResponse);

    // Save to database if user is logged in
    const { data: { user } } = await supabase.auth.getUser();
    if (user && finalResponse.arabicText && finalResponse.transliteration) {
      await supabase.from('transliterations').insert({
        user_id: user.id,
        original_text: finalResponse.arabicText,
        transliterated_text: finalResponse.transliteration,
        metadata: {
          source: 'image',
          translation: finalResponse.translation,
        },
      });
    }
  } catch (error) {
    onError(error instanceof Error ? error : new Error('An unexpected error occurred'));
  }
};

export const transliterateImageStream = async (
  imageData: File,
  onEvent: (event: ImageTransliterationEvent) => void,
): Promise<void> => {
  try {
    const reader = new FileReader();
    const base64Image = await new Promise<string>((resolve, reject) => {
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(imageData);
    });

    const { data: { session } } = await supabase.auth.getSession();
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    const authToken = session?.access_token || anonKey;
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const functionUrl = `${supabaseUrl}/functions/v1/transliterate-image`;

    const headers: Record<string, string> = { 'Content-Type': 'application/json' };
    headers['Authorization'] = `Bearer ${authToken}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ imageData: base64Image, stream: true }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const streamReader = response.body?.getReader();
    if (!streamReader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await streamReader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      while (true) {
        const lineEnd = buffer.indexOf('\n');
        if (lineEnd === -1) break;

        const line = buffer.slice(0, lineEnd).trim();
        buffer = buffer.slice(lineEnd + 1);

        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.error) {
              throw new Error(parsed.error.error || 'Stream error');
            }
            
            if (parsed.stage === 'started') {
              onEvent({ type: 'stage', stage: 'ocr' });
            } else if (parsed.stage === 'ocr_complete') {
              onEvent({ type: 'stage', stage: 'transliteration', arabicText: parsed.arabicText });
            } else if (parsed.stage === 'transliteration_complete') {
              onEvent({ type: 'complete', response: parsed });
              // Save to DB after completion
              const { data: { user } } = await supabase.auth.getUser();
              if (user) {
                await supabase.from('transliterations').insert({
                  user_id: user.id,
                  original_text: parsed.arabicText,
                  transliterated_text: parsed.transliteration,
                  metadata: { source: 'image', translation: parsed.translation },
                });
              }
            }
          } catch (e) {
            // Ignore invalid JSON
          }
        }
      }
    }
  } catch (error) {
    onEvent({ type: 'error', error: error instanceof Error ? error : new Error('An unexpected error occurred') });
  }
};

// Audio transliteration function
export const transliterateAudio = async (
  audioData: File | string,
  onComplete: (response: AudioTransliterationResponse) => void,
  onError: (error: Error) => void
): Promise<void> => {
  try {
    let base64Audio: string;
    if (audioData instanceof File) {
      const reader = new FileReader();
      base64Audio = await new Promise<string>((resolve, reject) => {
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(audioData);
      });
    } else {
      base64Audio = audioData;
    }

    const { data: { session } } = await supabase.auth.getSession();
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    const authToken = session?.access_token || anonKey;
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const functionUrl = `${supabaseUrl}/functions/v1/transliterate-audio`;

    const headers: Record<string, string> = { 'Content-Type': 'application/json' };
    headers['Authorization'] = `Bearer ${authToken}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ audioData: base64Audio }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const finalResponse: AudioTransliterationResponse = await response.json();
    onComplete(finalResponse);

    // Save to database if user is logged in
    const { data: { user } } = await supabase.auth.getUser();
    if (user && finalResponse.arabicText && finalResponse.transliteration) {
      await supabase.from('transliterations').insert({
        user_id: user.id,
        original_text: finalResponse.arabicText,
        transliterated_text: finalResponse.transliteration,
        metadata: {
          source: 'audio',
          translation: finalResponse.translation,
          audioUrl: finalResponse.audioUrl || null,
        },
      });
    }
  } catch (error) {
    onError(error instanceof Error ? error : new Error('An unexpected error occurred'));
  }
};

export const transliterateAudioStream = async (
  audioData: File,
  onEvent: (event: AudioTransliterationEvent) => void,
): Promise<void> => {
  try {
    const reader = new FileReader();
    const base64Audio = await new Promise<string>((resolve, reject) => {
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(audioData);
    });

    const { data: { session } } = await supabase.auth.getSession();
    const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    const authToken = session?.access_token || anonKey;
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const functionUrl = `${supabaseUrl}/functions/v1/transliterate-audio`;

    const headers: Record<string, string> = { 'Content-Type': 'application/json' };
    headers['Authorization'] = `Bearer ${authToken}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ audioData: base64Audio, stream: true }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const streamReader = response.body?.getReader();
    if (!streamReader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await streamReader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      while (true) {
        const lineEnd = buffer.indexOf('\n');
        if (lineEnd === -1) break;

        const line = buffer.slice(0, lineEnd).trim();
        buffer = buffer.slice(lineEnd + 1);

        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.error) {
              throw new Error(parsed.error.error || 'Stream error');
            }
            
            if (parsed.stage === 'started') {
              onEvent({ type: 'stage', stage: 'processing' });
            } else if (parsed.stage === 'transcription_complete') {
              onEvent({ type: 'stage', stage: 'transcription', arabicText: parsed.arabicText });
            } else if (parsed.stage === 'transliteration_complete') {
              onEvent({ type: 'complete', response: parsed });
              // Save to DB after completion
              const { data: { user } } = await supabase.auth.getUser();
              if (user) {
                await supabase.from('transliterations').insert({
                  user_id: user.id,
                  original_text: parsed.arabicText,
                  transliterated_text: parsed.transliteration,
                  metadata: { 
                    source: 'audio', 
                    translation: parsed.translation,
                    audioUrl: parsed.audioUrl || null
                  },
                });
              }
            }
          } catch (e) {
            // Ignore invalid JSON
          }
        }
      }
    }
  } catch (error) {
    onEvent({ type: 'error', error: error instanceof Error ? error : new Error('An unexpected error occurred') });
  }
};

// Legacy function for backward compatibility
export const transliterateTextLegacy = async (text: string): Promise<string> => {
  const result = await transliterateText({ text });
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.transliterated_text || '';
};
