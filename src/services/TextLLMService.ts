import { supabase } from '@/integrations/supabase/client';
import {
  TransformRequest,
  TransformResponse,
  TransliterationRequest,
  TransliterationResponse,
  TranslationRequest,
  TranslationResponse
} from '@/shared/types/api.types';

import { deepMerge } from '@/shared/lib/utils';

export class TextLLMService {
  private abortController: AbortController | null = null;
  private transliterationController: AbortController | null = null;
  private translationController: AbortController | null = null;

  // Helper to check for and extract content from a raw stream chunk
  private extractContent(chunk: any): string | null {
    return chunk?.choices?.[0]?.delta?.content || null;
  }

  /**
   * Stream text transliteration and translation from the edge function
   */
  async streamTransform(
    request: TransformRequest,
    onChunk: (data: Partial<TransformResponse>) => void,
    onComplete?: (data: TransformResponse) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    this.abortController = new AbortController();

    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/transliterate`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': session ? `Bearer ${session.access_token}` : '',
          },
          body: JSON.stringify({
            text: request.text,
            sourceLang: request.sourceLang,
            targetLang: request.targetLang,
            stream: false, // Force non-streaming for now
          }),
          signal: this.abortController.signal,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Check if response is JSON (non-streaming)
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        onChunk(data);
        onComplete?.(data);
        return;
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('Response body is not readable');
      }

      let buffer = '';
      let accumulatedText = '';
      let finalResponse: TransformResponse | null = null;

      while (true) {
        const { value, done } = await reader.read();
        
        if (done) {
          // If we have a final response, use it
          if (finalResponse) {
            onComplete?.(finalResponse);
          }
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        // Keep the last line in buffer if it's not complete
        buffer = lines[lines.length - 1];
        
        // Process all complete lines
        for (let i = 0; i < lines.length - 1; i++) {
          const line = lines[i].trim();
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'chunk') {
                // Accumulate raw text chunks
                accumulatedText += parsed.text;
                // For now, show accumulated text as transliteration during streaming
                onChunk({ transliteration: accumulatedText });
              } else if (parsed.type === 'complete') {
                // Final parsed response with both transliteration and translation
                finalResponse = {
                  transliteration: parsed.transliteration || accumulatedText,
                  translation: parsed.translation || '',
                };
                // Send the complete response immediately
                onChunk(finalResponse);
                onComplete?.(finalResponse);
              }
            } catch (e) {
              console.error('Failed to parse SSE data:', e, 'Line:', line);
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('Stream aborted');
          return;
        }
        onError?.(error);
      }
      throw error;
    }
  }

  /**
   * Get transliteration only
   */
  async getTransliteration(
    request: TransliterationRequest,
    onChunk?: (data: Partial<TransliterationResponse>) => void,
    onComplete?: (data: TransliterationResponse) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    this.transliterationController = new AbortController();

    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/transliterate`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': session ? `Bearer ${session.access_token}` : '',
          },
          body: JSON.stringify({
            text: request.text,
            sourceLang: request.sourceLang,
            stream: request.stream || false,
          }),
          signal: this.transliterationController.signal,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle streaming vs. non-streaming
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        onChunk?.(data);
        onComplete?.(data);
        return;
      }

      // Handle stream
      const reader = response.body?.getReader();
      if (!reader) throw new Error('Response body is not readable');
      const decoder = new TextDecoder();
      let buffer = '';
      let jsonBuffer = '';
      let finalResponse: TransliterationResponse | null = null;

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          if (jsonBuffer && !finalResponse) {
            try {
              finalResponse = JSON.parse(jsonBuffer);
              onComplete?.(finalResponse);
            } catch (e) {
              console.error('Failed to parse final JSON from buffer:', e, 'Buffer:', jsonBuffer);
            }
          }
          break;
        }
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              if (jsonBuffer && !finalResponse) {
                try {
                  finalResponse = JSON.parse(jsonBuffer);
                  onComplete?.(finalResponse);
                } catch (e) {
                  console.error('Failed to parse JSON at [DONE]:', e, 'Buffer:', jsonBuffer);
                }
              }
              return;
            }
            try {
              const parsed = JSON.parse(data);
              if (parsed.delta) {
                const content = this.extractContent(parsed.delta);
                if (content) {
                  jsonBuffer += content;
                  // A simple but effective way to provide partial updates
                  // is to send the whole buffer and let the UI diff it.
                  // This is not true partial JSON parsing, but it works for this case.
                  onChunk?.({ transliteration: jsonBuffer.replace(/\\"/g, '"').replace(/^\{"transliteration":"/, '').replace(/"\}$/, '') });
                }
                if (parsed.delta?.choices?.[0]?.finish_reason === 'stop') {
                  finalResponse = JSON.parse(jsonBuffer);
                  onComplete?.(finalResponse);
                  return;
                }
              } else if (parsed.error) {
                throw new Error(`Stream error: ${parsed.error.message}`);
              }
            } catch (e) {
              // Ignore JSON parse errors on individual lines, as we are accumulating a buffer
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('Transliteration aborted');
          return;
        }
        onError?.(error);
      }
      throw error;
    }
  }
/**
   * Get transliteration for already-translated text.
   * The source language for this operation is the *target* language of the initial translation.
   */
  async getPostTransliteration(
    request: TransliterationRequest,
    onChunk?: (data: Partial<TransliterationResponse>) => void,
    onComplete?: (data: TransliterationResponse) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // This is essentially a wrapper around getTransliteration,
    // but it's a separate method to make the client-side orchestration clearer.
    // The key is that the `sourceLang` in the request will be the *opposite*
    // of the user's original input language.
    return this.getTransliteration(request, onChunk, onComplete, onError);
  }

  /**
   * Get translation only
   */
  async getTranslation(
    request: TranslationRequest,
    onChunk?: (data: Partial<TranslationResponse>) => void,
    onComplete?: (data: TranslationResponse) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    this.translationController = new AbortController();

    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/translate`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': session ? `Bearer ${session.access_token}` : '',
          },
          body: JSON.stringify({
            text: request.text,
            sourceLang: request.sourceLang,
            targetLang: request.targetLang,
            stream: request.stream || false,
          }),
          signal: this.translationController.signal,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle streaming vs. non-streaming
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        onChunk?.(data);
        onComplete?.(data);
        return;
      }

      // Handle stream
      const reader = response.body?.getReader();
      if (!reader) throw new Error('Response body is not readable');
      const decoder = new TextDecoder();
      let buffer = '';
      let jsonBuffer = '';
      let finalResponse: TranslationResponse | null = null;

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          if (jsonBuffer && !finalResponse) {
            try {
              finalResponse = JSON.parse(jsonBuffer);
              onComplete?.(finalResponse);
            } catch (e) {
              console.error('Failed to parse final JSON from buffer:', e, 'Buffer:', jsonBuffer);
            }
          }
          break;
        }
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              if (jsonBuffer && !finalResponse) {
                try {
                  finalResponse = JSON.parse(jsonBuffer);
                  onComplete?.(finalResponse);
                } catch (e) {
                  console.error('Failed to parse JSON at [DONE]:', e, 'Buffer:', jsonBuffer);
                }
              }
              return;
            }
            try {
              const parsed = JSON.parse(data);
              if (parsed.delta) {
                const content = this.extractContent(parsed.delta);
                if (content) {
                  jsonBuffer += content;
                  onChunk?.({ translation: jsonBuffer.replace(/\\"/g, '"').replace(/^\{"translation":"/, '').replace(/"\}$/, '') });
                }
                if (parsed.delta?.choices?.[0]?.finish_reason === 'stop') {
                  finalResponse = JSON.parse(jsonBuffer);
                  onComplete?.(finalResponse);
                  return;
                }
              } else if (parsed.error) {
                throw new Error(`Stream error: ${parsed.error.message}`);
              }
            } catch (e) {
              // Ignore JSON parse errors on individual lines
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('Translation aborted');
          return;
        }
        onError?.(error);
      }
      throw error;
    }
  }

  /**
   * Get both transliteration and translation in parallel
   */
  async getTransformParallel(
    request: TransformRequest,
    callbacks: {
      onTransliterationChunk?: (data: Partial<TransliterationResponse>) => void;
      onTransliterationComplete?: (data: TransliterationResponse) => void;
      onTransliterationError?: (error: Error) => void;
      onTranslationChunk?: (data: Partial<TranslationResponse>) => void;
      onTranslationComplete?: (data: TranslationResponse) => void;
      onTranslationError?: (error: Error) => void;
    }
  ): Promise<void> {
    // Launch both requests in parallel
    const promises = [
      this.getTransliteration(
        {
          text: request.text,
          sourceLang: request.sourceLang,
          stream: request.stream ?? true,
        },
        callbacks.onTransliterationChunk,
        callbacks.onTransliterationComplete,
        callbacks.onTransliterationError
      ).catch(error => {
        console.error('Transliteration failed:', error);
        callbacks.onTransliterationError?.(error);
      }),
      this.getTranslation(
        {
          text: request.text,
          sourceLang: request.sourceLang,
          targetLang: request.targetLang,
          stream: request.stream ?? true,
        },
        callbacks.onTranslationChunk,
        callbacks.onTranslationComplete,
        callbacks.onTranslationError
      ).catch(error => {
        console.error('Translation failed:', error);
        callbacks.onTranslationError?.(error);
      })
    ];

    // Wait for both to complete (or fail)
    await Promise.allSettled(promises);
  }

  /**
   * Abort specific operations
   */
  abortTransliteration(): void {
    this.transliterationController?.abort();
    this.transliterationController = null;
  }

  abortTranslation(): void {
    this.translationController?.abort();
    this.translationController = null;
  }

  abortAll(): void {
    this.abort();
    this.abortTransliteration();
    this.abortTranslation();
  }

  /**
   * Abort the current streaming operation (legacy method)
   */
  abort(): void {
    this.abortController?.abort();
    this.abortController = null;
  }
}

// Export a singleton instance
export const textLLMService = new TextLLMService();