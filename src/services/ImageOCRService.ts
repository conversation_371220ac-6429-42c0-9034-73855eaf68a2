import { supabase } from '@/integrations/supabase/client';
import { TransformResponse, OperationStatus } from '@/shared/types/api.types';
import { textLLMService } from './TextLLMService';

export interface ImageOCRRequest {
  image: File;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
}

export interface ImageOCRResponse extends TransformResponse {
  extractedText?: string;
  transliterationStatus?: OperationStatus<string>;
  translationStatus?: OperationStatus<string>;
}

export type ImageOCRStage = 'uploading' | 'ocr' | 'transliterating' | 'translating' | 'done';

export interface ImageOCREvent {
  type: 'stage' | 'complete' | 'error' | 'partial' | 'transliteration_chunk' | 'translation_chunk';
  stage?: ImageOCRStage;
  response?: ImageOCRResponse;
  error?: Error;
  delta?: any;
}

export class ImageOCRService {
  // Helper to check for and extract content from a raw stream chunk
  private extractContent(chunk: any): string | null {
    return chunk?.choices?.[0]?.delta?.content || null;
  }

  /**
   * Process image through OCR and parallel transliteration/translation pipeline
   */
  async processImage(
    request: ImageOCRRequest,
    onEvent: (event: ImageOCREvent) => void
  ): Promise<void> {
    try {
      onEvent({ type: 'stage', stage: 'uploading' });

      const formData = new FormData();
      formData.append('image', request.image);
      formData.append('sourceLang', request.sourceLang);
      formData.append('targetLang', request.targetLang);
      formData.append('stream', 'true'); // Enable streaming

      const { data: { session } } = await supabase.auth.getSession();
      
      // Use session token if available, otherwise use anon key
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY;

      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/transliterate-image`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle stream
      const reader = response.body?.getReader();
      if (!reader) throw new Error('Response body is not readable');
      const decoder = new TextDecoder();
      let buffer = '';
      let accumulatedResponse: ImageOCRResponse = {};
      let transliterationJsonBuffer = '';
      let translationJsonBuffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          onEvent({ type: 'complete', response: accumulatedResponse });
          break;
        }
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6);
            if (dataStr === '[DONE]') {
              onEvent({ type: 'stage', stage: 'done' });
              onEvent({ type: 'complete', response: accumulatedResponse });
              return;
            }
            try {
              const parsed = JSON.parse(dataStr);
              if (parsed.error) {
                throw new Error(`Stream error: ${parsed.error.message}`);
              }

              if (parsed.stage) {
                onEvent({ type: 'stage', stage: parsed.stage });
                if (parsed.extractedText) {
                  accumulatedResponse.extractedText = parsed.extractedText;
                  onEvent({ type: 'partial', response: { extractedText: parsed.extractedText } });
                }
              } else if (parsed.type === 'transliteration_chunk') {
                const content = this.extractContent(parsed.delta);
                if (content) {
                  transliterationJsonBuffer += content;
                  try {
                    const parsedChunk = JSON.parse(transliterationJsonBuffer);
                    accumulatedResponse.transliteration = parsedChunk.transliteration;
                    onEvent({ type: 'partial', response: { transliteration: parsedChunk.transliteration } });
                  } catch (e) {
                    // In-progress JSON, send the partial string
                    onEvent({ type: 'partial', response: { transliteration: transliterationJsonBuffer } });
                  }
                }
              } else if (parsed.type === 'translation_chunk') {
                const content = this.extractContent(parsed.delta);
                if (content) {
                  translationJsonBuffer += content;
                  try {
                    const parsedChunk = JSON.parse(translationJsonBuffer);
                    accumulatedResponse.translation = parsedChunk.translation;
                    onEvent({ type: 'partial', response: { translation: parsedChunk.translation } });
                  } catch (e) {
                    // In-progress JSON, send the partial string
                    onEvent({ type: 'partial', response: { translation: translationJsonBuffer } });
                  }
                }
              }
            } catch (e) {
              // This can happen when parsing partial JSON from the stream.
              // We can ignore these errors as we accumulate the full response.
            }
          }
        }
      }
    } catch (error) {
      onEvent({
        type: 'error',
        error: error instanceof Error ? error : new Error('Unknown error')
      });
      throw error;
    }
  }
}

// Export a singleton instance
export const imageOCRService = new ImageOCRService();