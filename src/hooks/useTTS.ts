import { useState, useCallback, useMemo } from 'react';
import { useMutation } from '@tanstack/react-query';
import { audioTTSService } from '@/services/AudioTTSService';
import { useToast } from '@/shared/hooks/use-toast';

export interface TTSCache {
  [key: string]: string; // cacheKey -> blobUrl
}

export interface UseTTSResult {
  generateAudio: (text: string, lang: 'ar' | 'en') => Promise<string>;
  play: (text: string, lang: 'ar' | 'en') => Promise<void>;
  stop: () => void;
  isGenerating: boolean;
  isPlaying: boolean;
  isLoadingAudio: (text: string, lang: 'ar' | 'en') => boolean;
  isPlayingAudio: (text: string, lang: 'ar' | 'en') => boolean;
  cache: TTSCache;
  clearCache: () => void;
}

export function useTTS(): UseTTSResult {
  const [cache, setCache] = useState<TTSCache>({});
  const [playingKey, setPlayingKey] = useState<string | null>(null);
  const [loadingKey, setLoadingKey] = useState<string | null>(null);
  const { toast } = useToast();

  const generateMutation = useMutation({
    mutationFn: async ({ text, lang }: { text: string; lang: 'ar' | 'en' }) => {
      const cacheKey = `${lang}:${text}`;
      
      // Check local state cache first
      if (cache[cacheKey]) {
        return cache[cacheKey];
      }

      // Generate audio
      const blobUrl = await audioTTSService.generateAudio({ text, lang });
      
      // Update local cache
      setCache(prev => ({ ...prev, [cacheKey]: blobUrl }));
      
      return blobUrl;
    },
    onError: (error: Error) => {
      toast({
        title: 'Audio Generation Failed',
        description: error.message || 'Failed to generate audio',
        variant: 'destructive',
      });
    },
  });

  const generateAudio = useCallback(
    async (text: string, lang: 'ar' | 'en'): Promise<string> => {
      return generateMutation.mutateAsync({ text, lang });
    },
    [generateMutation]
  );

  const play = useCallback(
    async (text: string, lang: 'ar' | 'en'): Promise<void> => {
      try {
        const cacheKey = `${lang}:${text}`;
        setLoadingKey(cacheKey);

        // Get or generate the audio URL
        let blobUrl = cache[cacheKey];
        if (!blobUrl) {
          blobUrl = await generateAudio(text, lang);
        }

        // Set playing state and clear loading
        setLoadingKey(null);
        setPlayingKey(cacheKey);

        // Play the audio
        await audioTTSService.play(blobUrl);
      } catch (error) {
        console.error('Audio playback failed:', error);
        toast({
          title: 'Playback Failed',
          description: 'Failed to play audio. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoadingKey(null);
        setPlayingKey(null);
      }
    },
    [cache, generateAudio, toast]
  );

  const stop = useCallback(() => {
    audioTTSService.stop();
    setPlayingKey(null);
  }, []);

  const clearCache = useCallback(() => {
    // Clean up blob URLs
    Object.values(cache).forEach(blobUrl => {
      URL.revokeObjectURL(blobUrl);
    });
    setCache({});
    // Also clear service-level cache
    audioTTSService.clearCache();
  }, [cache]);

  const isPlaying = useMemo(() => playingKey !== null, [playingKey]);

  const isLoadingAudio = useCallback(
    (text: string, lang: 'ar' | 'en') => {
      const cacheKey = `${lang}:${text}`;
      return loadingKey === cacheKey;
    },
    [loadingKey]
  );

  const isPlayingAudio = useCallback(
    (text: string, lang: 'ar' | 'en') => {
      const cacheKey = `${lang}:${text}`;
      return playingKey === cacheKey;
    },
    [playingKey]
  );

  return {
    generateAudio,
    play,
    stop,
    isGenerating: generateMutation.isLoading,
    isPlaying,
    isLoadingAudio,
    isPlayingAudio,
    cache,
    clearCache,
  };
}