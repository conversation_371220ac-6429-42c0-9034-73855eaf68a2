import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { imageOCRService, ImageOCRRequest, ImageOCRResponse, ImageOCREvent, ImageOCRStage } from '@/services/ImageOCRService';
import { useToast } from '@/shared/hooks/use-toast';
import { OperationStatus } from '@/shared/types/api.types';

export interface UseOCRResult {
  processImage: (image: File, sourceLang: 'ar' | 'en', targetLang: 'ar' | 'en') => Promise<void>;
  isProcessing: boolean;
  stage: ImageOCRStage;
  progress: number;
  result: ImageOCRResponse | null;
  error: Error | null;
  reset: () => void;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
}

export function useOCR(): UseOCRResult {
  const [stage, setStage] = useState<ImageOCRStage>('uploading');
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<ImageOCRResponse | null>(null);
  const [transliterationStatus, setTransliterationStatus] = useState<OperationStatus<string>>({ status: 'idle' });
  const [translationStatus, setTranslationStatus] = useState<OperationStatus<string>>({ status: 'idle' });
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: async (request: ImageOCRRequest) => {
      setResult(null);
      setStage('uploading');
      setProgress(0);
      setTransliterationStatus({ status: 'idle' });
      setTranslationStatus({ status: 'idle' });

      let latestResult: ImageOCRResponse | null = null;

      await imageOCRService.processImage(request, (event: ImageOCREvent) => {
        if (event.type === 'stage' && event.stage) {
          setStage(event.stage);
          // Update progress based on stage
          switch (event.stage) {
            case 'uploading':
              setProgress(10);
              break;
            case 'ocr_complete':
              setProgress(50);
              setTransliterationStatus({ status: 'loading' });
              setTranslationStatus({ status: 'loading' });
              break;
            case 'done':
              setProgress(100);
              setTransliterationStatus({ status: 'success', data: latestResult?.transliteration });
              setTranslationStatus({ status: 'success', data: latestResult?.translation });
              break;
          }
        } else if (event.type === 'partial' && event.response) {
          console.log('[useOCR] Partial event received:', event.response);
          setResult(prev => ({ ...prev, ...event.response }));
          latestResult = { ...latestResult, ...event.response };
        } else if (event.type === 'complete' && event.response) {
          console.log('[useOCR] Complete event received:', event.response);
          setResult(event.response);
          latestResult = event.response;
          setProgress(100);
        } else if (event.type === 'error' && event.error) {
          throw event.error;
        }
      });
      return latestResult;
    },
    onError: (error: Error) => {
      setStage('uploading');
      setProgress(0);
      setTransliterationStatus({ status: 'error', error: error.message });
      setTranslationStatus({ status: 'error', error: error.message });
      toast({
        title: 'Error',
        description: error.message || 'Failed to process image',
        variant: 'destructive',
      });
    },
    onSuccess: (data) => {
      if (data) {
        setResult(data);
      }
    },
  });

  const processImage = useCallback(
    async (image: File, sourceLang: 'ar' | 'en', targetLang: 'ar' | 'en') => {
      await mutation.mutateAsync({ image, sourceLang, targetLang });
    },
    [mutation]
  );

  const reset = useCallback(() => {
    setStage('uploading');
    setProgress(0);
    setResult(null);
    setTransliterationStatus({ status: 'idle' });
    setTranslationStatus({ status: 'idle' });
    mutation.reset();
  }, [mutation]);

  return {
    processImage,
    isProcessing: mutation.isPending,
    stage,
    progress,
    result: result,
    error: mutation.error as Error | null,
    reset,
    transliterationStatus,
    translationStatus,
  };
}