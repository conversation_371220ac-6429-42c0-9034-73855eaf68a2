// Set dark mode by default, respecting user preference if set
const savedTheme = localStorage.getItem('theme');
if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
  document.documentElement.classList.add('dark');
} else {
  document.documentElement.classList.remove('dark');
}
import { createRoot } from 'react-dom/client'
import App from './app/App.tsx'
import ErrorBoundary from './shared/components/ErrorBoundary.tsx'
import { validateEnvironment, displayEnvironmentErrors } from './shared/utils/env-validation.ts'
import './index.css'

// Validate environment variables before starting the app
const envValidation = validateEnvironment();

if (!envValidation.isValid) {
  displayEnvironmentErrors(envValidation.errors);
  
  // Create a simple error display for production
  const rootElement = document.getElementById("root");
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f0fdf4, #fef3c7);
        font-family: system-ui, -apple-system, sans-serif;
        padding: 2rem;
      ">
        <div style="
          max-width: 600px;
          background: white;
          padding: 2rem;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0,0,0,0.1);
          border: 2px solid #10b981;
        ">
          <h1 style="
            color: #065f46;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
          ">⚠️ Configuration Error</h1>
          <p style="
            color: #374151;
            margin-bottom: 1rem;
            line-height: 1.6;
          ">
            SalahScribe requires proper environment configuration to function correctly.
          </p>
          <div style="
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
          ">
            <h3 style="color: #dc2626; margin-bottom: 0.5rem; font-weight: 600;">Missing Configuration:</h3>
            <ul style="color: #7f1d1d; margin: 0; padding-left: 1.5rem;">
              ${envValidation.errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
          </div>
          <p style="
            color: #6b7280;
            font-size: 0.875rem;
            line-height: 1.5;
          ">
            Please check your environment variables and refresh the page.
            Refer to the project documentation for setup instructions.
          </p>
        </div>
      </div>
    `;
  }
  
  // Don't proceed with app initialization
  throw new Error('Environment validation failed. Check console for details.');
}

// Log successful validation in development
if (import.meta.env.DEV) {
  console.log('✅ Environment validation passed');
  console.log('📊 Configuration loaded:', {
    supabaseConfigured: !!envValidation.config?.VITE_SUPABASE_URL,
  });
}

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element not found");
}

createRoot(rootElement).render(
  <ErrorBoundary>
    <App />
  </ErrorBoundary>
);
