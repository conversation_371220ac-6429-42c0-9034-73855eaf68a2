import type { User, Session, ApiResponse } from './common.types';

// Authentication related types

export interface AuthUser extends User {
  email_confirmed_at?: string;
  phone?: string;
  phone_confirmed_at?: string;
  last_sign_in_at?: string;
  app_metadata?: Record<string, unknown>;
  user_metadata?: Record<string, unknown>;
  identities?: AuthIdentity[];
}

export interface AuthIdentity {
  id: string;
  user_id: string;
  identity_data?: Record<string, unknown>;
  provider: string;
  created_at: string;
  updated_at: string;
}

export interface AuthSession extends Session {
  user: AuthUser;
}

export interface SignUpCredentials {
  email: string;
  password: string;
  options?: {
    data?: Record<string, unknown>;
    captchaToken?: string;
  };
}

export interface SignInCredentials {
  email: string;
  password: string;
  options?: {
    captchaToken?: string;
  };
}

export interface ResetPasswordCredentials {
  email: string;
  options?: {
    captchaToken?: string;
  };
}

export interface UpdatePasswordCredentials {
  password: string;
}

export interface AuthContextType {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  signUp: (credentials: SignUpCredentials) => Promise<ApiResponse<AuthUser>>;
  signIn: (credentials: SignInCredentials) => Promise<ApiResponse<AuthSession>>;
  signOut: () => Promise<ApiResponse<void>>;
  resetPassword: (credentials: ResetPasswordCredentials) => Promise<ApiResponse<void>>;
  updatePassword: (credentials: UpdatePasswordCredentials) => Promise<ApiResponse<AuthUser>>;
  refreshSession: () => Promise<ApiResponse<AuthSession>>;
}

export interface AuthError {
  message: string;
  status?: number;
  name?: string;
}

export type AuthEventType = 
  | 'SIGNED_IN'
  | 'SIGNED_OUT'
  | 'TOKEN_REFRESHED'
  | 'USER_UPDATED'
  | 'PASSWORD_RECOVERY';

export interface AuthEvent {
  event: AuthEventType;
  session: AuthSession | null;
}

export type AuthChangeEvent = (event: AuthEvent) => void;