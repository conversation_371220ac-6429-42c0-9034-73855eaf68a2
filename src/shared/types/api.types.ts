import type { ApiResponse, QueryParams } from './common.types';

// API related types

export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: unknown;
  params?: QueryParams;
  timeout?: number;
}

export interface ApiClient {
  get<T = unknown>(url: string, config?: ApiRequestConfig): Promise<ApiResponse<T>>;
  post<T = unknown>(url: string, data?: unknown, config?: ApiRequestConfig): Promise<ApiResponse<T>>;
  put<T = unknown>(url: string, data?: unknown, config?: ApiRequestConfig): Promise<ApiResponse<T>>;
  patch<T = unknown>(url: string, data?: unknown, config?: ApiRequestConfig): Promise<ApiResponse<T>>;
  delete<T = unknown>(url: string, config?: ApiRequestConfig): Promise<ApiResponse<T>>;
}

// New unified transformation types
export interface TransformRequest {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  text: string;
}

export interface TransformResponse {
  transliteration: string;
  translation: string;
}

// NEW: Separate types for individual operations
export interface TransliterationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  stream?: boolean;
}

export interface TransliterationResponse {
  transliteration: string;
}

export interface TranslationRequest {
  text: string;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  stream?: boolean;
}

export interface TranslationResponse {
  translation: string;
}

// Operation status for UI state management
export interface OperationStatus<T> {
  status: 'idle' | 'loading' | 'success' | 'error';
  data?: T;
  error?: string;
}

export interface AudioRequest {
  text: string;
  lang: 'ar' | 'en';
}

export type AudioResponse = ArrayBuffer; // PCM or mp3

// Legacy types - to be removed after migration
export interface LegacyTransliterationRequest {
  text: string;
  from_script?: string;
  to_script?: string;
  options?: {
    preserve_case?: boolean;
    preserve_punctuation?: boolean;
  };
}

export interface LegacyTransliterationResponse {
  transliterated_text: string;
  original_text: string;
  from_script: string;
  to_script: string;
  confidence?: number;
  alternatives?: string[];
}
export interface ImageTransliterationRequest {
  imageData: string; // base64 encoded image or URL
  stream?: boolean;
}

export interface ImageTransliterationResponse {
  arabicText: string;
  transliteration: string;
  translation: string;
}

export interface TransliterationError {
  message: string;
  code: string;
  details?: {
    invalid_characters?: string[];
    unsupported_script?: string;
  };
}

export interface SupabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
}

export interface SupabaseResponse<T = unknown> {
  data: T | null;
  error: SupabaseError | null;
  count?: number | null;
  status: number;
  statusText: string;
}

export interface DatabaseTable {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile extends DatabaseTable {
  user_id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  preferences?: Record<string, unknown>;
}

export interface TransliterationHistory extends DatabaseTable {
  user_id: string;
  original_text: string;
  transliterated_text: string;
  from_script: string;
  to_script: string;
  is_favorite?: boolean;
}

export type SupabaseTable = 'user_profiles' | 'transliteration_history';

export interface SupabaseQueryBuilder<T = unknown> {
  select(columns?: string): SupabaseQueryBuilder<T>;
  insert(values: Partial<T> | Partial<T>[]): SupabaseQueryBuilder<T>;
  update(values: Partial<T>): SupabaseQueryBuilder<T>;
  delete(): SupabaseQueryBuilder<T>;
  eq(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  neq(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  gt(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  gte(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  lt(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  lte(column: keyof T, value: unknown): SupabaseQueryBuilder<T>;
  like(column: keyof T, pattern: string): SupabaseQueryBuilder<T>;
  ilike(column: keyof T, pattern: string): SupabaseQueryBuilder<T>;
  in(column: keyof T, values: unknown[]): SupabaseQueryBuilder<T>;
  order(column: keyof T, options?: { ascending?: boolean }): SupabaseQueryBuilder<T>;
  limit(count: number): SupabaseQueryBuilder<T>;
  range(from: number, to: number): SupabaseQueryBuilder<T>;
}