// Image transliteration related types

export interface ImageTransliterationRequest {
  image: string; // Base64 data URL or regular URL
  format?: 'base64' | 'url';
  options?: {
    language?: string;
    enhanceQuality?: boolean;
  };
}

export interface ImageTransliterationResponse {
  arabicText: string;
  transliteration: string;
  translation: string;
}

export type ImageTransliterationStage = 'uploading' | 'ocr' | 'transliteration' | 'done';

export interface ImageUploadValidation {
  maxSizeBytes: number;
  allowedFormats: string[];
  allowedMimeTypes: string[];
}

export const IMAGE_UPLOAD_CONFIG: ImageUploadValidation = {
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
  allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp']
};