import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

interface WebVitalsMetrics {
  CLS?: PerformanceMetrics;
  FID?: PerformanceMetrics;
  FCP?: PerformanceMetrics;
  LCP?: PerformanceMetrics;
  TTFB?: PerformanceMetrics;
}

/**
 * Hook for monitoring web vitals and performance metrics
 */
export const usePerformanceMonitoring = (enabled: boolean = true) => {
  const metricsRef = useRef<WebVitalsMetrics>({});
  const observerRef = useRef<PerformanceObserver | null>(null);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    // Function to rate performance metrics
    const rateMetric = (name: string, value: number): 'good' | 'needs-improvement' | 'poor' => {
      const thresholds: Record<string, [number, number]> = {
        CLS: [0.1, 0.25],
        FID: [100, 300],
        FCP: [1800, 3000],
        LCP: [2500, 4000],
        TTFB: [800, 1800],
      };

      const [good, poor] = thresholds[name] || [0, 0];
      if (value <= good) return 'good';
      if (value <= poor) return 'needs-improvement';
      return 'poor';
    };

    // Function to log metrics
    const logMetric = (name: string, value: number) => {
      const rating = rateMetric(name, value);
      const metric: PerformanceMetrics = {
        name,
        value,
        rating,
        timestamp: Date.now(),
      };

      metricsRef.current[name as keyof WebVitalsMetrics] = metric;

      // Log to console in development
      if (import.meta.env.DEV) {
        const emoji = rating === 'good' ? '✅' : rating === 'needs-improvement' ? '⚠️' : '❌';
        console.log(`${emoji} ${name}: ${value.toFixed(2)}ms (${rating})`);
      }

      // In production, you could send this to an analytics service
      if (import.meta.env.PROD) {
        // Example: sendToAnalytics(metric);
      }
    };

    // Observe paint metrics (FCP)
    const paintObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          logMetric('FCP', entry.startTime);
        }
      }
    });

    // Observe layout shift metrics (CLS)
    const layoutShiftObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      for (const entry of list.getEntries()) {
        const layoutShiftEntry = entry as PerformanceEntry & {
          hadRecentInput?: boolean;
          value?: number;
        };
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value || 0;
        }
      }
      if (clsValue > 0) {
        logMetric('CLS', clsValue);
      }
    });

    // Observe largest contentful paint (LCP)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        logMetric('LCP', lastEntry.startTime);
      }
    });

    // Observe first input delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const firstInputEntry = entry as PerformanceEntry & {
          processingStart?: number;
        };
        if (firstInputEntry.processingStart) {
          logMetric('FID', firstInputEntry.processingStart - entry.startTime);
        }
      }
    });

    try {
      paintObserver.observe({ entryTypes: ['paint'] });
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });

      observerRef.current = paintObserver; // Store one for cleanup
    } catch (error) {
      console.warn('Performance monitoring not supported:', error);
    }

    // Measure TTFB
    const measureTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        logMetric('TTFB', ttfb);
      }
    };

    // Measure TTFB after page load
    if (document.readyState === 'complete') {
      measureTTFB();
    } else {
      window.addEventListener('load', measureTTFB);
    }

    return () => {
      try {
        paintObserver.disconnect();
        layoutShiftObserver.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
      } catch (error) {
        console.warn('Error disconnecting performance observers:', error);
      }
    };
  }, [enabled]);

  // Function to get current metrics
  const getMetrics = (): WebVitalsMetrics => {
    return { ...metricsRef.current };
  };

  // Function to manually track custom metrics
  const trackCustomMetric = (name: string, value: number) => {
    if (enabled) {
      const metric: PerformanceMetrics = {
        name,
        value,
        rating: 'good', // Custom metrics don't have predefined ratings
        timestamp: Date.now(),
      };

      if (import.meta.env.DEV) {
        console.log(`📊 Custom metric - ${name}: ${value}`);
      }

      // In production, send to analytics
      if (import.meta.env.PROD) {
        // Example: sendToAnalytics(metric);
      }
    }
  };

  return {
    getMetrics,
    trackCustomMetric,
  };
};

/**
 * Hook for tracking page navigation performance
 */
export const useNavigationTracking = (enabled: boolean = true) => {
  const startTimeRef = useRef<number>(0);

  const startNavigation = (routeName: string) => {
    if (!enabled) return;
    
    startTimeRef.current = performance.now();
    
    if (import.meta.env.DEV) {
      console.log(`🚀 Navigation started to: ${routeName}`);
    }
  };

  const endNavigation = (routeName: string) => {
    if (!enabled || !startTimeRef.current) return;
    
    const duration = performance.now() - startTimeRef.current;
    
    if (import.meta.env.DEV) {
      console.log(`✅ Navigation completed to: ${routeName} (${duration.toFixed(2)}ms)`);
    }

    // In production, send to analytics
    if (import.meta.env.PROD) {
      // Example: sendNavigationMetric(routeName, duration);
    }

    startTimeRef.current = 0;
  };

  return {
    startNavigation,
    endNavigation,
  };
};

/**
 * Hook for tracking component render performance
 */
export const useRenderTracking = (componentName: string, enabled: boolean = true) => {
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    renderCountRef.current += 1;
    const currentTime = performance.now();
    
    if (lastRenderTimeRef.current > 0) {
      const timeSinceLastRender = currentTime - lastRenderTimeRef.current;
      
      if (import.meta.env.DEV && timeSinceLastRender < 16) {
        console.warn(`⚠️ Fast re-render detected in ${componentName}: ${timeSinceLastRender.toFixed(2)}ms`);
      }
    }
    
    lastRenderTimeRef.current = currentTime;

    if (import.meta.env.DEV && renderCountRef.current % 10 === 0) {
      console.log(`📈 ${componentName} has rendered ${renderCountRef.current} times`);
    }
  });

  return {
    renderCount: renderCountRef.current,
  };
};