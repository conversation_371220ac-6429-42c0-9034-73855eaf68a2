import { useState, useCallback, useRef } from 'react';
import { useToast } from '@/shared/hooks/use-toast';
import { textLLMService } from '@/services/TextLLMService';
import type { OperationStatus } from '@/shared/types/api.types';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  transliteration?: string;
  translation?: string;
  original?: string;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
}

interface UseTransliterationReturn {
  messages: Message[];
  isLoading: boolean;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
  addMessage: (content: string, sourceLang: 'ar' | 'en', targetLang: 'ar' | 'en') => Promise<void>;
  clearMessages: () => void;
  retryTransliteration: (messageId: string) => Promise<void>;
  retryTranslation: (messageId: string) => Promise<void>;
}

const INITIAL_MESSAGE: Message = {
  id: '1',
  type: 'assistant',
  content: 'Assalā<PERSON> ʿalaykum! Welcome to SalahScribe. I can help you transliterate and translate Arabic du\'as and Islamic texts. Please paste your text below.',
  timestamp: new Date(),
  transliterationStatus: { status: 'idle' },
  translationStatus: { status: 'idle' }
};

export const useTransliteration = (): UseTransliterationReturn => {
  const [messages, setMessages] = useState<Message[]>([INITIAL_MESSAGE]);
  const [isLoading, setIsLoading] = useState(false);
  const [transliterationStatus, setTransliterationStatus] = useState<OperationStatus<string>>({ status: 'idle' });
  const [translationStatus, setTranslationStatus] = useState<OperationStatus<string>>({ status: 'idle' });
  const { toast } = useToast();

  const addMessage = useCallback(async (content: string, sourceLang: 'ar' | 'en', targetLang: 'ar' | 'en'): Promise<void> => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date(),
      transliterationStatus: { status: 'idle' },
      translationStatus: { status: 'idle' }
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setTransliterationStatus({ status: 'loading' });
    setTranslationStatus({ status: 'loading' });

    // Create assistant message
    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessage: Message = {
      id: assistantMessageId,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      original: content,
      transliterationStatus: { status: 'loading' },
      translationStatus: { status: 'loading' }
    };
    
    setMessages(prev => [...prev, assistantMessage]);

    try {
      await textLLMService.getTransformParallel(
        {
          text: content,
          sourceLang,
          targetLang
        },
        {
          // Transliteration callbacks
          onTransliterationComplete: (transliteration) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    transliteration,
                    transliterationStatus: { status: 'success', data: transliteration }
                  }
                : msg
            ));
            setTransliterationStatus({ status: 'success', data: transliteration });
          },
          onTransliterationError: (error) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    transliterationStatus: { status: 'error', error: error.message }
                  }
                : msg
            ));
            setTransliterationStatus({ status: 'error', error: error.message });
          },
          // Translation callbacks
          onTranslationComplete: (translation) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    translation,
                    translationStatus: { status: 'success', data: translation }
                  }
                : msg
            ));
            setTranslationStatus({ status: 'success', data: translation });
          },
          onTranslationError: (error) => {
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    translationStatus: { status: 'error', error: error.message }
                  }
                : msg
            ));
            setTranslationStatus({ status: 'error', error: error.message });
          }
        }
      );
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process text. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const retryTransliteration = useCallback(async (messageId: string): Promise<void> => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.original) return;

    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, transliterationStatus: { status: 'loading' } }
        : msg
    ));

    try {
      await textLLMService.getTransliteration(
        {
          text: message.original,
          sourceLang: message.original.match(/[\u0600-\u06FF]/) ? 'ar' : 'en',
        },
        undefined,
        (transliteration) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  transliteration,
                  transliterationStatus: { status: 'success', data: transliteration }
                }
              : msg
          ));
        },
        (error) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  transliterationStatus: { status: 'error', error: error.message }
                }
              : msg
          ));
        }
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to retry transliteration",
        variant: "destructive"
      });
    }
  }, [messages, toast]);

  const retryTranslation = useCallback(async (messageId: string): Promise<void> => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.original) return;

    const sourceLang = message.original.match(/[\u0600-\u06FF]/) ? 'ar' : 'en';
    const targetLang = sourceLang === 'ar' ? 'en' : 'ar';

    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, translationStatus: { status: 'loading' } }
        : msg
    ));

    try {
      await textLLMService.getTranslation(
        {
          text: message.original,
          sourceLang,
          targetLang,
        },
        undefined,
        (translation) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  translation,
                  translationStatus: { status: 'success', data: translation }
                }
              : msg
          ));
        },
        (error) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  translationStatus: { status: 'error', error: error.message }
                }
              : msg
          ));
        }
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to retry translation",
        variant: "destructive"
      });
    }
  }, [messages, toast]);

  const clearMessages = useCallback(() => {
    setMessages([INITIAL_MESSAGE]);
  }, []);

  return {
    messages,
    isLoading,
    transliterationStatus,
    translationStatus,
    addMessage,
    clearMessages,
    retryTransliteration,
    retryTranslation
  };
};
