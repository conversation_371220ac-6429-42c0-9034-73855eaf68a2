import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/shared/hooks/use-toast';
import { useAuth } from '@/app/contexts/AuthContext';

interface AuthFormData {
  email: string;
  password: string;
  fullName: string;
}

interface AuthFormErrors {
  email?: string;
  password?: string;
  fullName?: string;
}

interface UseAuthFormReturn {
  formData: AuthFormData;
  errors: AuthFormErrors;
  isLoading: boolean;
  isLogin: boolean;
  setIsLogin: (isLogin: boolean) => void;
  updateField: (field: keyof AuthFormData, value: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  clearErrors: () => void;
}

const validateEmail = (email: string): string | undefined => {
  if (!email) return 'Email is required';
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) return 'Please enter a valid email address';
  return undefined;
};

const validatePassword = (password: string): string | undefined => {
  if (!password) return 'Password is required';
  if (password.length < 6) return 'Password must be at least 6 characters';
  return undefined;
};

const validateFullName = (fullName: string, isRequired: boolean): string | undefined => {
  if (isRequired && !fullName.trim()) return 'Full name is required';
  return undefined;
};

export const useAuthForm = (): UseAuthFormReturn => {
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
    fullName: ''
  });
  const [errors, setErrors] = useState<AuthFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  
  const { signIn, signUp } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const updateField = useCallback((field: keyof AuthFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const validateForm = useCallback((): boolean => {
    const newErrors: AuthFormErrors = {};
    
    newErrors.email = validateEmail(formData.email);
    newErrors.password = validatePassword(formData.password);
    
    if (!isLogin) {
      newErrors.fullName = validateFullName(formData.fullName, true);
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error !== undefined);
  }, [formData, isLogin]);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      if (isLogin) {
        const { data, error } = await signIn({ 
          email: formData.email, 
          password: formData.password 
        });
        
        if (error) {
          if (error.message.includes('Invalid login credentials')) {
            toast({
              title: "Login Failed",
              description: "Invalid email or password. Please check your credentials.",
              variant: "destructive"
            });
          } else {
            toast({
              title: "Login Failed",
              description: error.message,
              variant: "destructive"
            });
          }
        } else if (data) {
          toast({
            title: "Welcome back!",
            description: "You have successfully signed in.",
          });
          navigate('/');
        }
      } else {
        const { data, error } = await signUp({ 
          email: formData.email, 
          password: formData.password, 
          fullName: formData.fullName 
        });
        
        if (error) {
          if (error.message.includes('User already registered')) {
            toast({
              title: "Account Exists",
              description: "This email is already registered. Please sign in instead.",
              variant: "destructive"
            });
          } else {
            toast({
              title: "Signup Failed",
              description: error.message,
              variant: "destructive"
            });
          }
        } else if (data) {
          toast({
            title: "Account Created!",
            description: "Please check your email to verify your account.",
          });
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [formData, isLogin, signIn, signUp, toast, navigate, validateForm]);

  return {
    formData,
    errors,
    isLoading,
    isLogin,
    setIsLogin,
    updateField,
    handleSubmit,
    clearErrors
  };
};