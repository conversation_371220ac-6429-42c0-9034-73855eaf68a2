/**
 * Environment validation utility
 * Validates required environment variables on app startup
 */

interface EnvironmentConfig {
  VITE_SUPABASE_URL?: string;
  VITE_SUPABASE_ANON_KEY?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  config?: EnvironmentConfig;
}

/**
 * Validates all required environment variables
 */
export const validateEnvironment = (): ValidationResult => {
  const errors: string[] = [];
  
  // Required environment variables
  const requiredVars = [
    // No required vars - all are optional now
  ] as const;

  // Optional environment variables (with warnings)
  const optionalVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ] as const;

  // Check required variables
  for (const varName of requiredVars) {
    const value = import.meta.env[varName];
    if (!value || value.trim() === '') {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  }

  // Check optional variables and warn if missing
  for (const varName of optionalVars) {
    const value = import.meta.env[varName];
    if (!value || value.trim() === '') {
      console.warn(`⚠️ Optional environment variable missing: ${varName}. Some features may not work properly.`);
    }
  }

  // Validate URL format for Supabase URL
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (supabaseUrl && !isValidUrl(supabaseUrl)) {
    errors.push('VITE_SUPABASE_URL must be a valid URL');
  }

  // Validate Supabase key format
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  if (supabaseKey && supabaseKey.length < 100) {
    errors.push('VITE_SUPABASE_ANON_KEY appears to be invalid (too short)');
  }

  const isValid = errors.length === 0;

  if (isValid) {
    return {
      isValid: true,
      errors: [],
      config: {
        VITE_SUPABASE_URL: supabaseUrl,
        VITE_SUPABASE_ANON_KEY: supabaseKey,
      }
    };
  }

  return {
    isValid: false,
    errors
  };
};

/**
 * Validates if a string is a valid URL
 */
const isValidUrl = (string: string): boolean => {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
};

/**
 * Displays environment validation errors in a user-friendly way
 */
export const displayEnvironmentErrors = (errors: string[]): void => {
  console.error('🚨 Environment Configuration Errors:');
  console.error('=====================================');
  
  errors.forEach((error, index) => {
    console.error(`${index + 1}. ${error}`);
  });
  
  console.error('');
  console.error('Please check your environment file and ensure all required variables are set.');
  console.error('Refer to .env.development.example, .env.staging.example, or .env.production.example for the required format.');
};

/**
 * Gets a validated environment variable with type safety
 */
export const getEnvVar = (key: keyof EnvironmentConfig): string => {
  const value = import.meta.env[key];
  if (!value) {
    throw new Error(`Environment variable ${key} is not defined`);
  }
  return value;
};

/**
 * Checks if the app is running in development mode
 */
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV;
};

/**
 * Checks if the app is running in production mode
 */
export const isProduction = (): boolean => {
  return import.meta.env.PROD;
};