import React from 'react';
import { Label } from '@/shared/components/ui/label';
import { Input } from '@/shared/components/ui/input';
import { Textarea } from '@/shared/components/ui/textarea';
import { cn } from '@/shared/lib/utils';

interface BaseFormFieldProps {
  label: string;
  id: string;
  error?: string;
  required?: boolean;
  className?: string;
  labelClassName?: string;
}

interface InputFormFieldProps extends BaseFormFieldProps {
  type: 'input';
  inputType?: 'text' | 'email' | 'password' | 'number';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

interface TextareaFormFieldProps extends BaseFormFieldProps {
  type: 'textarea';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  rows?: number;
  dir?: 'ltr' | 'rtl';
}

type FormFieldProps = InputFormFieldProps | TextareaFormFieldProps;

const FormField = React.memo<FormFieldProps>((props) => {
  const {
    label,
    id,
    error,
    required = false,
    className,
    labelClassName,
    type
  } = props;

  const baseInputClasses = cn(
    'border-emerald-300 focus:border-emerald-500 focus:ring-emerald-500',
    'dark:border-emerald-600 dark:focus:border-emerald-400',
    error && 'border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600',
    className
  );

  const renderInput = () => {
    if (type === 'input') {
      const { inputType = 'text', value, onChange, placeholder, disabled } = props;
      return (
        <Input
          id={id}
          type={inputType}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={baseInputClasses}
          disabled={disabled}
          required={required}
          aria-describedby={error ? `${id}-error` : undefined}
        />
      );
    }

    if (type === 'textarea') {
      const { value, onChange, placeholder, disabled, rows = 3, dir } = props;
      return (
        <Textarea
          id={id}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={baseInputClasses}
          disabled={disabled}
          required={required}
          rows={rows}
          dir={dir}
          aria-describedby={error ? `${id}-error` : undefined}
        />
      );
    }

    return null;
  };

  return (
    <div className="space-y-2">
      <Label 
        htmlFor={id} 
        className={cn(
          'text-emerald-700 dark:text-emerald-300',
          error && 'text-red-700 dark:text-red-300',
          labelClassName
        )}
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {renderInput()}
      
      {error && (
        <p 
          id={`${id}-error`}
          className="text-sm text-red-600 dark:text-red-400"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  );
});

FormField.displayName = 'FormField';

export default FormField;