import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in dev
    if (process.env.NODE_ENV === 'dev') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // In production, you might want to log to an error reporting service
    // Example: logErrorToService(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-cream-50 to-amber-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
          {/* Islamic geometric pattern overlay */}
          <div className="absolute inset-0 opacity-5">
            <div className="w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }} />
          </div>

          <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
            <div className="text-center max-w-md mx-auto">
              {/* Header Section */}
              <div className="mb-8">
                {/* Icon with Glow */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-white to-slate-100 rounded-full blur-xl opacity-30 animate-glow-pulse transform scale-150"></div>
                  <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-slate-100 dark:from-slate-700 dark:to-slate-600 rounded-full flex items-center justify-center mx-auto relative z-10 border-2 border-emerald-200 dark:border-slate-600 shadow-xl">
                    <AlertTriangle className="w-10 h-10 text-emerald-600 dark:text-slate-400" />
                  </div>
                </div>

                {/* Title */}
                <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-700 via-emerald-600 to-teal-600 dark:from-slate-200 dark:via-slate-300 dark:to-slate-100 bg-clip-text text-transparent mb-4">
                  Something went wrong
                </h1>
                
                {/* Subtitle */}
                <p className="text-emerald-600 dark:text-slate-400 font-medium mb-6">
                  We apologize for the inconvenience. An unexpected error has occurred.
                </p>
              </div>

              {/* Error Details for Dev */}
              {process.env.NODE_ENV === 'dev' && this.state.error && (
                <div className="mb-6">
                  <details className="text-left bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg p-4 border border-emerald-200 dark:border-slate-600 shadow-lg">
                    <summary className="cursor-pointer font-medium text-emerald-700 dark:text-slate-300 mb-3 text-sm">
                      Error Details (Development)
                    </summary>
                    <pre className="whitespace-pre-wrap text-emerald-600 dark:text-slate-400 overflow-auto text-xs bg-emerald-50 dark:bg-slate-700 p-3 rounded border">
                      {this.state.error.message}
                      {this.state.error.stack && `\n\n${this.state.error.stack}`}
                    </pre>
                  </details>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={this.handleReset}
                  variant="outline"
                  className="flex-1 border-emerald-300 dark:border-slate-600 text-emerald-700 dark:text-slate-300 hover:bg-emerald-50 dark:hover:bg-slate-700 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg transition-all duration-200 hover:shadow-xl"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button
                  onClick={this.handleGoHome}
                  className="flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 dark:from-slate-700 dark:to-slate-800 dark:hover:from-slate-600 dark:hover:to-slate-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-2 border-emerald-500 dark:border-slate-600"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {/* Footer */}
              <div className="mt-8 text-emerald-600/70 dark:text-slate-400">
                <p className="text-xs font-medium">
                  Built with reverence for Islamic scholarship
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;