import * as React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>ontent, CardTitle } from '@/shared/components/ui/card';
import { cn } from '@/shared/lib/utils';

interface ResultCardProps {
  title: string;
  children: React.ReactNode;
  rightAction?: React.ReactNode;
  className?: string;
}

export default function ResultCard({ title, children, rightAction, className }: ResultCardProps) {
  return (
    <Card className={cn("border border-emerald-100 dark:border-slate-700 shadow-md bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm", className)}>
      <CardHeader className="flex flex-row justify-between items-center p-3 border-b border-emerald-100 dark:border-slate-700">
        <CardTitle className="text-sm font-semibold text-emerald-800 dark:text-slate-200">{title}</CardTitle>
        {rightAction}
      </CardHeader>
      <CardContent className="p-4">
        {children}
      </CardContent>
    </Card>
  );
}