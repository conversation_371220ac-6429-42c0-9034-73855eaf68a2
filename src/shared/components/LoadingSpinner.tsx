import React from 'react';
import { cn } from '@/shared/lib/utils';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

const LoadingSpinner = React.memo<LoadingSpinnerProps>(({
  size = 'md',
  className,
  text
}) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className={cn('flex items-center justify-center gap-2', className)}>
      <div className="flex items-center gap-1">
        <div 
          className={cn(
            'bg-slate-500 dark:bg-slate-400 rounded-full animate-bounce',
            sizeClasses[size]
          )}
          style={{ animationDelay: '0ms' }}
        />
        <div 
          className={cn(
            'bg-slate-500 dark:bg-slate-400 rounded-full animate-bounce',
            sizeClasses[size]
          )}
          style={{ animationDelay: '150ms' }}
        />
        <div 
          className={cn(
            'bg-slate-500 dark:bg-slate-400 rounded-full animate-bounce',
            sizeClasses[size]
          )}
          style={{ animationDelay: '300ms' }}
        />
      </div>
      {text && (
        <span className={cn(
          'text-slate-600 dark:text-slate-400 font-medium',
          textSizeClasses[size]
        )}>
          {text}
        </span>
      )}
    </div>
  );
});

LoadingSpinner.displayName = 'LoadingSpinner';

export default LoadingSpinner;