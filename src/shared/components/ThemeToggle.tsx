
import React from 'react';
import { <PERSON>, Sun } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { useTheme } from '@/app/contexts/ThemeContext';

const ThemeToggle = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="w-10 h-10 rounded-full hover:bg-emerald-100 dark:hover:bg-emerald-800 transition-colors"
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {isDark ? (
        <Sun className="w-5 h-5 text-emerald-600 dark:text-slate-400" />
      ) : (
        <Moon className="w-5 h-5 text-emerald-600" />
      )}
    </Button>
  );
};

export default React.memo(ThemeToggle);
