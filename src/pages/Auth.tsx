
import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { <PERSON>, Star, ArrowLeft } from 'lucide-react';
import { useAuthForm } from '@/shared/hooks/useAuthForm';
import { useRenderTracking } from '@/shared/hooks/usePerformanceMonitoring';
import FormField from '@/shared/components/FormField';

const Auth = () => {
  // Performance tracking
  useRenderTracking('Auth');

  const navigate = useNavigate();
  const {
    formData,
    errors,
    isLoading,
    isLogin,
    setIsLogin,
    updateField,
    handleSubmit
  } = useAuthForm();

  // Memoize navigation handler
  const handleBackToHome = useCallback(() => {
    navigate('/');
  }, [navigate]);

  // Memoize toggle handler
  const handleToggleMode = useCallback(() => {
    setIsLogin(!isLogin);
  }, [isLogin, setIsLogin]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-cream-50 to-amber-50 flex items-center justify-center p-4">
      {/* Islamic geometric pattern overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="relative z-10 w-full max-w-md">
        <Card className="border-2 border-emerald-200 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-600 to-emerald-800 rounded-lg flex items-center justify-center">
                <Moon className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-amber-600 bg-clip-text text-transparent">
                SalahScribe
              </h1>
              <Star className="w-5 h-5 text-amber-500" />
            </div>
            <CardTitle className="text-xl text-emerald-800">
              {isLogin ? 'Welcome Back' : 'Create Account'}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              {!isLogin && (
                <FormField
                  type="input"
                  id="fullName"
                  label="Full Name"
                  inputType="text"
                  value={formData.fullName}
                  onChange={(value) => updateField('fullName', value)}
                  placeholder="Enter your full name"
                  required={!isLogin}
                  error={errors.fullName}
                />
              )}
              
              <FormField
                type="input"
                id="email"
                label="Email"
                inputType="email"
                value={formData.email}
                onChange={(value) => updateField('email', value)}
                placeholder="Enter your email"
                required
                error={errors.email}
              />
              
              <FormField
                type="input"
                id="password"
                label="Password"
                inputType="password"
                value={formData.password}
                onChange={(value) => updateField('password', value)}
                placeholder="Enter your password"
                required
                error={errors.password}
              />
              
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white"
              >
                {isLoading ? 'Processing...' : (isLogin ? 'Sign In' : 'Create Account')}
              </Button>
            </form>
            
            <div className="text-center">
              <Button
                variant="ghost"
                onClick={handleToggleMode}
                className="text-emerald-600 hover:text-emerald-700"
              >
                {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
              </Button>
            </div>
            
            <div className="text-center">
              <Button
                variant="ghost"
                onClick={handleBackToHome}
                className="text-emerald-600 hover:text-emerald-700 flex items-center gap-2 mx-auto"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default React.memo(Auth);
