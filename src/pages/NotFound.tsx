import React from "react";
import { useLocation, Link } from "react-router-dom";
import { Button } from "@/shared/components/ui/button";
import { Home, AlertCircle } from "lucide-react";
import { useRenderTracking } from "@/shared/hooks/usePerformanceMonitoring";

const NotFound = (): JSX.Element => {
  // Performance tracking
  useRenderTracking('NotFound');
  
  const location = useLocation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-cream-50 to-amber-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Islamic geometric pattern overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="text-center max-w-md mx-auto">
          {/* Header Section */}
          <div className="mb-8">
            {/* Icon with Glow */}
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-white to-slate-100 rounded-full blur-xl opacity-30 animate-glow-pulse transform scale-150"></div>
              <div className="w-24 h-24 bg-gradient-to-br from-emerald-100 to-slate-100 dark:from-slate-700 dark:to-slate-600 rounded-full flex items-center justify-center mx-auto relative z-10 border-2 border-emerald-200 dark:border-slate-600 shadow-xl">
                <AlertCircle className="w-12 h-12 text-emerald-600 dark:text-slate-400" />
              </div>
            </div>

            {/* 404 Number */}
            <h1 className="text-6xl font-bold bg-gradient-to-r from-emerald-700 via-emerald-600 to-teal-600 dark:from-slate-200 dark:via-slate-300 dark:to-slate-100 bg-clip-text text-transparent mb-4 animate-gradient bg-300%">
              404
            </h1>
            
            {/* Title */}
            <h2 className="text-2xl font-semibold bg-gradient-to-r from-emerald-700 to-teal-600 dark:from-slate-300 dark:to-slate-100 bg-clip-text text-transparent mb-4">
              Page Not Found
            </h2>
            
            {/* Description */}
            <p className="text-emerald-600 dark:text-slate-400 font-medium mb-6">
              The page{' '}
              <code className="bg-emerald-100 dark:bg-slate-700 px-2 py-1 rounded text-sm font-mono border border-emerald-200 dark:border-slate-600">
                {location.pathname}
              </code>{' '}
              could not be found.
            </p>
          </div>
          
          {/* Action Button */}
          <Link to="/">
            <Button className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 dark:from-slate-700 dark:to-slate-800 dark:hover:from-slate-600 dark:hover:to-slate-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-2 border-emerald-500 dark:border-slate-600 px-8 py-3">
              <Home className="w-4 h-4 mr-2" />
              Return to Home
            </Button>
          </Link>

          {/* Footer */}
          <div className="mt-8 text-emerald-600/70 dark:text-slate-400">
            <p className="text-xs font-medium">
              Built with reverence for Islamic scholarship
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(NotFound);
