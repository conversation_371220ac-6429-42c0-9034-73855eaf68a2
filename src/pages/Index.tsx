import React, { useState, useCallback } from 'react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { useAuth } from '@/app/contexts/AuthContext';
import { useRenderTracking } from '@/shared/hooks/usePerformanceMonitoring';
import ThemeToggle from '@/shared/components/ThemeToggle';
import LoadingSpinner from '@/shared/components/LoadingSpinner';
import UnifiedTransformForm from '@/features/transform/UnifiedTransformForm';
import { UnifiedMessage } from '@/features/transform/types/message.types';
import { ArrowRight } from 'lucide-react';

const Index = () => {
  // Performance tracking
  useRenderTracking('Index');

  const { loading } = useAuth();
  
  // Shared messages state for both tabs
  const [messages, setMessages] = useState<UnifiedMessage[]>([]);
  const [activeTab, setActiveTab] = useState('ar-en');
  
  // Wrap setMessages in useCallback to prevent unnecessary re-renders
  const handleSetMessages = useCallback((updater: React.SetStateAction<UnifiedMessage[]>) => {
    setMessages(updater);
  }, []);

  // Handle tab changes (simplified - no scroll preservation needed)
  const handleTabChange = useCallback((newTab: string) => {
    setActiveTab(newTab);
  }, []);

  // Calculate source and target languages based on active tab
  const sourceLang = activeTab === 'ar-en' ? 'ar' : 'en';
  const targetLang = activeTab === 'ar-en' ? 'en' : 'ar';

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-cream-50 to-amber-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return (
    <div className="h-screen md:h-[80vh] flex flex-col bg-gradient-to-br from-emerald-50 via-cream-50 to-amber-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 overflow-hidden">
      {/* Islamic geometric pattern overlay */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div className="w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.4'%3E%3Cpath d='M30 30l15-15v30l-15-15zm0 0l-15 15h30l-15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-3 sm:py-8 pb-28 max-w-4xl flex flex-col h-full">
        {/* Header */}
        <div className="text-center mb-3 sm:mb-6 pt-2 sm:pt-0">
          <div className="flex items-center justify-center gap-3 mb-1 sm:mb-3 relative">
            {/* <div className="absolute right-0 top-0">
              <ThemeToggle />
            </div> */}
            
            {/* Animated Icon with Glow */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-white to-slate-100 rounded-full blur-xl opacity-30 animate-glow-pulse"></div>
              <img
                src="/inverted_icon.svg"
                alt="SalahScribe Icon"
                className="w-10 h-10 relative z-10"
              />
            </div>
            
            {/* Animated Title with Enhanced Gradient */}
            <div className="relative">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-700 via-emerald-600 to-teal-600 dark:from-slate-200 dark:via-slate-300 dark:to-slate-100 bg-clip-text text-transparent animate-gradient bg-300% drop-shadow-sm">
                SalahScribe
              </h1>
            </div>
          </div>
          
          {/* Animated Tagline */}
          <div className="mb-2 sm:mb-0">
            <p className="text-sm text-emerald-600 dark:text-slate-400 font-medium tracking-wide">
              Read, speak & understand Arabic — instantly.
            </p>
          </div>
        </div>

        {/* Main Interface */}
        <Card className="border-2 border-emerald-200 dark:border-slate-600 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-xl overflow-hidden flex flex-col flex-1 md:flex-initial md:min-h-[40vh]">
          <CardContent className="p-0 flex flex-col h-full">
            {/* Tab selector (visual only) */}
            <div className="grid w-full grid-cols-2 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-slate-700 dark:to-slate-700 p-1 gap-1 border-b border-emerald-200 dark:border-slate-600">
              <button
                onClick={() => handleTabChange('ar-en')}
                className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                  activeTab === 'ar-en'
                    ? 'bg-white dark:bg-slate-600 shadow-md'
                    : 'hover:bg-white/50 dark:hover:bg-slate-600/50'
                }`}
              >
                <span>Arabic</span>
                <ArrowRight className="w-4 h-4 flex-shrink-0" />
                <span>English</span>
              </button>
              <button
                onClick={() => handleTabChange('en-ar')}
                className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                  activeTab === 'en-ar'
                    ? 'bg-white dark:bg-slate-600 shadow-md'
                    : 'hover:bg-white/50 dark:hover:bg-slate-600/50'
                }`}
              >
                <span>English</span>
                <ArrowRight className="w-4 h-4 flex-shrink-0" />
                <span>Arabic</span>
              </button>
            </div>
            
            {/* Single form instance that never gets unmounted */}
            <div className="flex-1 min-h-0">
              <UnifiedTransformForm
                sourceLang={sourceLang}
                targetLang={targetLang}
                messages={messages}
                setMessages={handleSetMessages}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
