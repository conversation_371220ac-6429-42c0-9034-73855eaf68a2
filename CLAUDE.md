# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server on port 8080
- `npm run build` - Production build
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint
- `npm run lint:css` - Run stylelint on CSS files

### Playwright End-to-End Testing

#### Setup

- Install Playwright and its browsers:
  ```
  npm install --save-dev @playwright/test
  npx playwright install
  ```

- (Linux only) If you see missing library errors, install browser dependencies:
  ```
  npx playwright install-deps
  ```

#### Running Tests

- To run all Playwright tests:
  ```
  npx playwright test
  ```

- To run a specific test file:
  ```
  npx playwright test tests/salahscribe.spec.ts
  ```

- For headed (non-headless) mode, add `--headed`:
  ```
  npx playwright test --headed
  ```

- For debugging, use the Playwright Inspector:
  ```
  npx playwright test --debug
  ```

#### Notes

- The dev server must be running on http://localhost:8080 before running tests.
- See `tests/salahscribe.spec.ts` for an example test of the main user flow.

### Vercel Deployment (Frontend)
- `npm run deploy:dev` - Deploy frontend to Vercel development
- `npm run deploy:staging` - Deploy frontend to Vercel staging
- `npm run deploy:prod` - Deploy frontend to Vercel production

### Supabase Deployment (Backend)

#### Development
- `npm run supabase:dev:env` - Deploy environment variables to development
- `npm run supabase:dev:functions` - Deploy edge functions to development
- `npm run supabase:dev` - Deploy both env vars and functions to development

#### Staging
- `npm run supabase:staging:env` - Deploy environment variables to staging
- `npm run supabase:staging:functions` - Deploy edge functions to staging
- `npm run supabase:staging` - Deploy both env vars and functions to staging

#### Production
- `npm run supabase:prod:env` - Deploy environment variables to production
- `npm run supabase:prod:functions` - Deploy edge functions to production
- `npm run supabase:prod` - Deploy both env vars and functions to production

### Package Management

#### Checking for Outdated Packages
- `npm outdated` - Check which packages have updates available

#### Updating Packages
- `npm update` - Update to latest compatible versions (respects semver ranges in package.json)

#### For major version updates, manually update package.json versions then:
- `rm -rf node_modules package-lock.json`
- `npm install`

#### Always test after major updates:
- `npm run lint`
- `npm run lint:css`
- `npm run build`
- `npm run preview`
- **Test thoroughly** after package updates, especially major versions
- **Check for breaking changes** in package changelogs before updating
- **Update dependencies incrementally** rather than all at once for easier debugging
- **Revert problematic packages** if they cause compatibility issues

## Architecture

SalahScribe is a React-based Arabic assistance application featuring a three-tab interface for bidirectional Arabic-English text transformation, image OCR processing, and audio generation.

### Tech Stack:
- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query + React Context
- **Routing**: React Router DOM
- **Authentication**: Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **Configuration**: Centralized in `/config` directory

### Application Structure

#### Main Interface
Unified chat-style interface located in `src/pages/Index.tsx` with a two-tab switcher:
- **Arabic → English (ar-en)**: Processes Arabic input (text/image) to English output
- **English → Arabic (en-ar)**: Processes English input (text/image) to Arabic output
- **Multi-modal Input**: Single form handles both text and image inputs seamlessly
- **Audio Input**: Placeholder buttons - disabled for future implementation

#### Routing Structure
- **Simple state-based routing**: Tab switching managed in `src/pages/Index.tsx` with local state
- **Legacy route components**: `src/features/text/TextPage.tsx` and `src/features/image/ImagePage.tsx` exist but are unused
- **Current implementation**: Single unified interface without separate routing for input types
- **Future consideration**: Could implement shareable URLs like `/ar-en` and `/en-ar` if needed

#### Component Structure
- `src/shared/components/` - Reusable UI components and shadcn/ui library
- `src/features/transform/` - Unified transformation components
  - `UnifiedTransformForm.tsx` - Chat-style interface handling text, image, and audio inputs
  - `types/message.types.ts` - TypeScript definitions for unified message system
- `src/features/text/` - Legacy routing components (unused)
  - `TextPage.tsx` - Tab wrapper for text routes (not currently used)
- `src/features/image/` - Legacy routing components (unused)
  - `ImagePage.tsx` - Tab wrapper for image routes (not currently used)
- `src/pages/` - Main page components (Index, Auth, NotFound)
- `src/app/` - App-level components and contexts (`App.tsx`, `AuthContext`, `ThemeContext`)

#### Shared Components
**Core Application Components:**
- `src/shared/components/ErrorBoundary.tsx` - React error boundary with Islamic-themed UI
  - Development error details display
  - Try Again and Go Home action buttons
  - Custom fallback UI with gradient backgrounds
  - Error logging and reporting hooks
- `src/shared/components/LoadingSpinner.tsx` - Unified loading indicator
  - Multiple sizes (xs, sm, lg) with muted slate colors
  - Optional text display with spinner
  - Optimized for integration with chat interface
- `src/shared/components/ThemeToggle.tsx` - Dark/light theme switching
  - Sun/Moon icons with smooth transitions
  - Emerald theme integration with accessibility labels
  - Memoized for performance

**Form and Display Components:**
- `src/shared/components/FormField.tsx` - Unified form field with validation
  - Supports input and textarea types with error state styling
  - RTL/LTR directional support for textareas
  - Accessibility features (aria-describedby, required indicators)
- `src/shared/components/ResultCard.tsx` - Consistent result display container
  - Header with title and optional right action
  - Emerald-themed styling with backdrop blur
  - Flexible children content area

#### Service Layer Architecture
- `src/services/TextLLMService.ts` - Text transformation with SSE streaming support
  - Handles streaming/non-streaming responses
  - Accumulates chunks and parses final JSON response
  - Abort controller for cancellation
- `src/services/ImageOCRService.ts` - Image OCR pipeline with stage events
  - FormData-based image upload
  - Stage progression: uploading → ocr → transliteration → done
  - Falls back to anon key for unauthenticated users
- `src/services/AudioTTSService.ts` - Audio generation with caching
  - GPT-4o Mini audio generation (not just OpenAI TTS)
  - Blob URL caching for performance
  - Automatic cleanup on page unload
- `src/services/transliterationService.ts` - Legacy service (being phased out)
- `src/integrations/supabase/` - Supabase client and type definitions

#### Type Contracts

**API Types** (`src/shared/types/api.types.ts`):
```typescript
// Core API interfaces
export interface TransformRequest {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  text: string;
}

export interface TransliterationRequest {
  sourceLang: 'ar' | 'en';
  text: string;
}

export interface TranslationRequest {
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  text: string;
}

export interface AudioRequest {
  text: string;
  lang: 'ar' | 'en';
}
```

**Message System Types** (`src/features/transform/types/message.types.ts`):
```typescript
export type InputType = 'text' | 'image' | 'audio';
export type MessageType = 'user' | 'assistant';

export interface OperationStatus<T> {
  status: 'idle' | 'loading' | 'success' | 'error';
  data?: T;
  error?: string;
}

export interface UnifiedMessage {
  id: string;
  type: MessageType;
  inputType: InputType;
  content: string;
  imageUrl?: string;
  imageFile?: File;
  timestamp: Date;
  sourceLang: 'ar' | 'en';
  targetLang: 'ar' | 'en';
  
  // Results
  extractedText?: string; // For OCR results
  transliteration?: string;
  translation?: string;
  original?: string; // Store original for audio playback
  postTransliteration?: string;
  
  // Status tracking
  ocrStatus?: OperationStatus<string>;
  transliterationStatus: OperationStatus<string>;
  translationStatus: OperationStatus<string>;
  postTransliterationStatus: OperationStatus<string>;
  
  // Progress for image processing
  stage?: 'uploading' | 'ocr' | 'transliterating' | 'translating' | 'done';
  progress?: number;
}
```

#### React Hooks

**Core Transformation Hooks:**
- `src/hooks/useOCR.ts` - Image processing orchestration
  - Stage tracking with progress percentages (10% → 25% → 60% → 100%)
  - React Query mutation wrapper
  - Automatic error toast notifications
  - Integrated with UnifiedTransformForm message state updates
- `src/hooks/useTTS.ts` - Audio generation and playback
  - Local state cache + service-level cache
  - Play method with automatic generation if not cached
  - Cleanup utilities for blob URLs
  - Used within UnifiedTransformForm for message-level audio controls
- `src/shared/hooks/useTransliteration.ts` - Legacy text transformation hook (unused in current unified interface)

**UI and Application Hooks:**
- `src/shared/hooks/use-mobile.tsx` - Responsive design utility
  - Detects mobile viewport using 768px breakpoint
  - Uses `window.matchMedia` with automatic updates on resize
  - Returns boolean for mobile state detection
- `src/shared/hooks/useAuthForm.ts` - Authentication form management
  - Complete login/signup form state management
  - Real-time validation with error clearing
  - Form submission with error handling and toast notifications
  - Navigation after successful authentication
  - Email/password validation utilities
- `src/shared/hooks/usePerformanceMonitoring.ts` - Performance monitoring system
  - Tracks Core Web Vitals: CLS, FID, FCP, LCP, TTFB
  - Automatic metric rating (good/needs-improvement/poor)
  - Development console logging with emoji indicators
  - Production-ready analytics integration hooks
  - Additional hooks: `useNavigationTracking`, `useRenderTracking`

#### Edge Functions
- `supabase/functions/transliterate/` - Transliteration-only endpoint (separated from translation)
- `supabase/functions/translate/` - Translation-only endpoint (new)
- `supabase/functions/transliterate-image/` - Image OCR and text processing (uses parallel calls)
- `supabase/functions/audio/` - Audio generation using GPT-4o Mini
- `supabase/functions/_shared/` - Shared utilities
  - `pipeline.ts` - Unified transform pipeline (planned)
  - `llm-providers.ts` - Multi-provider abstraction with structured output support
  - `audio-providers.ts` - Audio generation providers
  - `unified-prompt.ts` - Legacy unified prompt templates
  - `transliteration-prompt.ts` - Dedicated transliteration prompts
  - `translation-prompt.ts` - Dedicated translation prompts
  - `response-parser.ts` - JSON extraction from markdown code blocks
  - `schemas.ts` - JSON Schema definitions for structured outputs
  - `cors.ts` - CORS handling with OPTIONS support
  - `env-config.ts` - Environment variable configuration
  - `internal-api.ts` - Internal API client for code reuse across endpoints
  - `validation.ts` - Centralized validation utilities with TypeScript guards
  - `error-handling.ts` - Standardized error handling and response formatting

### Core Functionality:

#### Text Processing
- **Form-based interface** with immediate results display
- **Bidirectional transformation**: Arabic→English and English→Arabic
- **Hybrid Parallel/Sequential API Architecture**:
  - **Parallel**: Initial transliteration and translation run independently for speed.
  - **Sequential**: A post-translation transliteration is chained to run only after the translation is complete.
  - All three results are streamed in real-time.
- **Independent error handling** and retry capability for each of the three operations.
- **Real-time response streaming** for all operations.
- **Separate prompt systems** for transliteration and translation
- **OpenAI Structured Outputs support** for clean JSON responses
  - All providers now support structured outputs (OpenAI, OpenRouter, Gemini)
  - Robust JSON extraction from markdown code blocks (fixes display issue)
- **Abort capability**: Separate abort controllers for each operation
- **Dual response handling**: Supports both streaming SSE and non-streaming JSON
- Services:
  - `TextLLMService.getTransliteration()` - Transliteration only
  - `TextLLMService.getTranslation()` - Translation only
  - `TextLLMService.getPostTransliteration()` - For the third, chained transliteration call
  - `TextLLMService.getTransformParallel()` - Parallel execution
  - `TextLLMService.streamTransform()` - Legacy unified streaming (backward compatibility)
- Edge functions:
  - `supabase/functions/transliterate/index.ts` - Transliteration endpoint
  - `supabase/functions/translate/index.ts` - Translation endpoint

#### Image Processing
- **Multi-step pipeline**: OCR → parallel (transliteration + translation)
- **FormData upload** with proper CORS and authentication handling
- **Visual staging indicators** showing upload → OCR → processing progress
- **Bidirectional support** for Arabic and English text extraction
- **Progress tracking**: 10% (uploading) → 25% (OCR) → 60% (processing) → 100% (done)
- **Parallel transformation** after OCR extraction for faster results
- **Drag-and-drop interface** with image preview
- Service: `ImageOCRService.processImage()` (updated to use parallel calls)
- Edge function: `supabase/functions/transliterate-image/index.ts`
- Hook: `useOCR()` for stage events and progress tracking

#### Audio Generation & Playback
- **Context-aware audio**: Smart audio buttons in each message bubble
- **Source text priority**: Transliteration button plays original Arabic text, not romanization
- **Text-to-Speech (TTS)** using GPT-4o Mini models (not just OpenAI TTS)
- **Dual endpoint support**: `/v1/audio/speech` (standard TTS) and `/v1/chat/completions` (audio modality)
- **Configurable voices** via environment variables (Arabic: onyx, English: onyx by default)
- **Multi-level caching**: Service-level cache + hook-level cache for blob URLs
- **Automatic cleanup**: Blob URLs revoked on page unload
- **Error handling**: Detailed MediaError detection with user-friendly messages
- **Integrated UI**: Audio controls embedded in message result cards
- Service: `AudioTTSService.generateAudio()` and `AudioTTSService.play()`
- Edge function: `supabase/functions/audio/index.ts`
- Hook: `useTTS()` for generation, playback, and cache management

#### LLM Provider Abstraction
- **Multi-provider support**: `supabase/functions/_shared/llm-providers.ts`
- **Text providers**: Gemini, OpenRouter, OpenAI - all with structured output support
  - OpenAI: Direct structured output via `response_format`
  - OpenRouter: Structured output via `response_format`
  - Gemini: Emulated via function calling
- **Image providers**: OpenRouter (with vision models), Gemini (with function calling for structured responses)
- **Provider factories**: `createLLMProvider()`, `createImageLLMProvider()`
- **Response parsing**: Robust JSON extraction from markdown-wrapped responses

#### Data Storage
- Form results displayed immediately in component state (not persisted to database yet)
- Authenticated users can store results in `transliterations` table

### Configuration Files
Located in `/config`:
- `vite.config.ts` - Vite build configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `eslint.config.js` - ESLint configuration
- `postcss.config.js` - PostCSS configuration
- `tsconfig.json` - TypeScript configuration
- `tsconfig.app.json` - App-specific TypeScript configuration
- `tsconfig.node.json` - Node-specific TypeScript configuration
- `components.json` - shadcn/ui components configuration
- `vercel-dev.json` - Development Vercel deployment configuration
- `vercel-staging.json` - Staging Vercel deployment configuration
- `vercel-prod.json` - Production Vercel deployment configuration
- `.env.development` - Development environment variables
- `.env.development.example` - Example for development env vars
- `.env.staging` - Staging environment variables
- `.env.staging.example` - Example for staging env vars
- `.env.production` - Production environment variables
- `.env.production.example` - Example for production env vars

### File Structure Overview
Current implementation follows unified feature-based organization:
```
src/features/
  transform/
    UnifiedTransformForm.tsx # Chat-style unified form for text/image/audio
    types/
      message.types.ts       # Message system type definitions
  text/                      # Legacy - unused routing components
    TextPage.tsx            # Tab wrapper for text routes
  image/                    # Legacy - unused routing components  
    ImagePage.tsx           # Tab wrapper for image routes
src/hooks/
  useOCR.ts                 # Image processing orchestration
  useTTS.ts                 # Audio generation and playback
src/services/
  TextLLMService.ts         # Text transformation API (parallel support)
  ImageOCRService.ts        # Image OCR pipeline (parallel calls)
  AudioTTSService.ts        # Audio generation service
supabase/functions/_shared/
  llm-providers.ts          # Multi-provider abstraction
  audio-providers.ts        # Audio generation providers
  transliteration-prompt.ts # Dedicated transliteration prompts
  translation-prompt.ts     # Dedicated translation prompts
  response-parser.ts        # JSON extraction from markdown code blocks
  schemas.ts               # JSON Schema definitions for structured outputs
  internal-api.ts          # Internal API client for code reuse
  validation.ts            # Centralized validation utilities
  error-handling.ts        # Standardized error handling
```

### Other Configuration Files
- Frontend: `src/shared/utils/env-validation.ts`
- Edge Functions: `supabase/functions/_shared/env-config.ts`

### Path Aliases 
(configured in `/config/vite.config.ts`)
- `@/` maps to `src/` directory
- `@/shared` maps to `src/shared/` directory
- `@/app` maps to `src/app/` directory
- `@/pages` maps to `src/pages/` directory
- `@/integrations` maps to `src/integrations/` directory

### Performance Optimizations
- Lazy loading for route components
- Manual chunk splitting in Vite config for vendor libraries
- React Query with optimized cache settings (5min stale, 10min cache)
- Performance monitoring system with Core Web Vitals tracking
- Sophisticated scroll management with user position detection
- Animation optimization (only latest message animates to prevent choppy stacking)
- Memoized components for frequently re-rendered elements

### Styling and Animations
**CSS Architecture:**
- **Tailwind CSS** with custom Islamic-inspired color palette (including `cream` color range)
- **Dark mode support** using class-based strategy with CSS variables
- **RTL/LTR support** with `.placeholder-ltr` utility for proper text direction
- **Custom animation system** optimized for chat interfaces

**Key Animations:**
- `message-enter-user` / `message-enter-assistant` - Smooth slide-in animations for chat messages
- `pulse-subtle`, `glow-pulse`, `gentle-bounce` - Loading and interactive animations
- `gradient` animation for background gradients
- `enhanced-smooth-scroll` for sophisticated auto-scroll behavior

**Animation Performance:**
- Only the latest message receives entry animations to prevent performance issues
- Cubic-bezier easing functions for smooth, natural motion
- Transition-optimized hover states with scale and transform effects
- Smart scroll behavior based on message count and user scroll position

### Recent Architectural Changes (January 2025)

#### Unified Chat Interface Architecture (Recent Commits: f27adc7, 613d5cc, 963b7af, b53c656, c4c8c2f)
The application has been completely refactored from separate text/image forms to a unified chat-style interface:

**Major UI/UX Changes:**
- **Single Unified Form**: `UnifiedTransformForm.tsx` replaces separate `TextTransformForm.tsx` and `ImageTransformForm.tsx`
- **Chat-Style Interface**: Message-based conversation layout with user and assistant bubbles
- **Multi-Modal Input**: Single textarea with buttons for text, image, and audio (future) inputs
- **Persistent Message History**: Messages persist across tab switches (ar-en ↔ en-ar)
- **Real-Time Progress**: Independent loading states for transliteration and translation operations
- **Smart Auto-Scroll**: Advanced scrolling behavior with performance optimizations for rapid updates

**Key Technical Features:**
- **Message System**: New `UnifiedMessage` type with support for multiple input types and operation statuses
- **Parallel Processing**: Transliteration and translation run independently with separate status tracking
- **Image Integration**: Drag-and-drop image upload integrated into chat flow with progress indicators
- **Enhanced Error Handling**: Individual retry buttons for failed transliteration/translation operations
- **Audio Context Awareness**: Smart audio playback (plays original Arabic for transliteration button)
- **Performance Optimizations**: Sophisticated scroll management and animation controls for smooth UX

**Implementation Details:**
- Unified state management through `messages` array with `UnifiedMessage[]` type
- Status tracking via `OperationStatus<T>` for each operation (OCR, transliteration, translation)
- Advanced scroll behavior with distance detection and smart auto/smooth/instant scrolling
- Integrated file upload with validation and preview within chat interface
- Enhanced accessibility with proper ARIA labels and keyboard navigation

**Files Replaced/Deprecated:**
- `src/features/text/TextTransformForm.tsx` - Removed (functionality merged into UnifiedTransformForm)
- `src/features/image/ImageTransformForm.tsx` - Removed (functionality merged into UnifiedTransformForm)
- `src/features/text/TextPage.tsx` - Legacy routing component (unused)
- `src/features/image/ImagePage.tsx` - Legacy routing component (unused)

**New Files Created:**
- `src/features/transform/UnifiedTransformForm.tsx` - New unified chat interface
- `src/features/transform/types/message.types.ts` - Message system type definitions

**Benefits:**
- **Improved UX**: Single, intuitive interface for all input types
- **Better Context**: Conversation history provides better user context
- **Reduced Complexity**: Eliminated separate routing and form management
- **Enhanced Accessibility**: Better screen reader support and keyboard navigation
- **Performance**: Optimized rendering and scroll management

**Recent Refinements (Latest Commits):**
- **Enhanced User Guidance**: Updated instructional text for better user onboarding (f27adc7, 613d5cc)
- **Animation Fixes**: Resolved tab switching animation conflicts for smoother transitions (963b7af)
- **Advanced Auto-Scroll**: Implemented sophisticated scroll behavior with debug logging and position detection (b53c656)
- **Improved Input Experience**: Added Enter key submission and removed timestamp display for cleaner interface (c4c8c2f)
- **Visual Polish**: Enhanced animations, gradients, and loading states throughout the interface (b72df00 "Frontend jazzed")

### Recent UI Improvements (January 2025)

#### Loading State Color Update
- Updated `LoadingSpinner` component to use muted slate colors (`text-slate-600 dark:text-slate-400`)
- Previous emerald colors were too prominent and didn't match the app's design system
- Loading text for "Transliterating..." and "Translating..." now blends better with the interface

### Recent Backend Improvements (January 2025)

#### Structured Output Implementation
OpenAI's Structured Outputs feature has been implemented across all translation and transliteration services to ensure consistent JSON responses:

**Key Features:**
- **Guaranteed JSON Format**: Responses always match the defined JSON Schema
- **No Parsing Required**: Eliminates extraction of JSON from markdown code blocks
- **Type Safety**: All responses validated against schemas
- **Provider Support**:
  - OpenAI: Full native support via `response_format`
  - OpenRouter: Structured output support via `response_format`
  - Gemini: Emulated through function calling

**Schema Definitions** (in `supabase/functions/_shared/schemas.ts`):
- `TranslationResponseSchema` - For translation-only responses
- `TransliterationResponseSchema` - For transliteration-only responses
- `CombinedResponseSchema` - For unified endpoints returning both
- `OCRResponseSchema` - For image text extraction

**Implementation Details:**
- Prompts simplified to remove JSON formatting instructions
- Response parsers expect pure JSON without markdown wrapping
- Automatic fallback for providers that don't support structured outputs
- All endpoints updated to use structured output schemas

#### Refactoring Improvements
Major refactoring to enhance code reuse and maintainability:

**1. Internal API Module** (`supabase/functions/_shared/internal-api.ts`):
- Image OCR endpoint now reuses translation/transliteration logic directly
- Eliminates code duplication across endpoints
- Single source of truth for transformation logic
- Direct function calls instead of HTTP requests for better performance

**2. Shared Validation Module** (`supabase/functions/_shared/validation.ts`):
- Centralized validation for all endpoints
- Type-safe validation with TypeScript guards
- Language validation: `validateLanguage()`
- Text validation with length checks: `validateText()`
- Image data validation: `validateImageData()`
- Generic request validation: `validateRequestFields()`

**3. Shared Error Handling** (`supabase/functions/_shared/error-handling.ts`):
- Custom `ApiError` class with status codes and reasons
- Standardized error response creation
- Automatic error categorization (auth, token limit, config errors)
- Error handling wrapper: `withErrorHandling()`

**4. Simplified Endpoint Structure**:
All endpoints now follow a consistent pattern with automatic error handling and validation.

**Benefits:**
- Reduced code duplication by ~40%
- Consistent behavior across all endpoints
- Easier maintenance and testing
- Better error tracking and debugging
- Improved performance through direct function calls

#### OCR Structured Output Fix
Fixed an issue where image OCR was returning plain text instead of JSON:

**Problem**: Some models (particularly `google/gemini-2.0-flash-exp:free` via OpenRouter) returned plain Arabic text instead of the expected JSON format.

**Solution**: Implemented automatic fallback in LLM providers that wraps plain text responses in the expected JSON format when:
1. Structured output is requested
2. Response is plain text (doesn't start with '{')
3. Schema name is 'ocr_result'

**Implementation**: Added wrapping logic in both `OpenRouterProvider` and `OpenAIProvider` that automatically formats plain text as `{ "extractedText": "..." }`.

**Benefits**:
- Works with both structured output-capable and legacy models
- No changes needed in endpoint code
- Graceful fallback handling
- Maintains consistent API contract

### Database Schema

#### Tables

##### profiles

| Column     | Type                  | Nullable | Default | Notes                          |
|------------|-----------------------|----------|---------|--------------------------------|
| id         | uuid                  | No       |         | Primary Key                   |
| email      | text                  | Yes      |         |                              |
| full_name  | text                  | Yes      |         |                              |
| created_at | timestamp with time zone | Yes      | now()   |                              |
| updated_at | timestamp with time zone | Yes      | now()   |                              |

##### transliterations

| Column             | Type                  | Nullable | Default           | Notes                          |
|--------------------|-----------------------|----------|-------------------|--------------------------------|
| id                 | uuid                  | No       | gen_random_uuid() | Primary Key                   |
| user_id            | uuid                  | Yes      |                   | Foreign Key to profiles.id    |
| original_text      | text                  | No       |                   |                              |
| transliterated_text | text                  | No       |                   |                              |
| created_at         | timestamp with time zone | Yes      | now()             |                              |

#### Relationships

- `profiles.id` references `auth.users.id` (foreign key constraint `profiles_id_fkey`)
- `transliterations.user_id` references `auth.users.id` (foreign key constraint `transliterations_user_id_fkey`)

#### Row-Level Security (RLS)

- RLS is enabled and enforced on both `profiles` and `transliterations` tables, integrated with Supabase Auth for secure access control.

#### Supabase Extensions

The following key extensions are installed in the Supabase database:

- `pgcrypto` - cryptographic functions
- `uuid-ossp` - UUID generation
- `vector` - vector data type and indexing for similarity search
- `pg_graphql` - GraphQL support
- `supabase_vault` - secret management
- `pg_stat_statements` - query performance monitoring
- `plpgsql` - procedural language support
- `pgjwt` - JSON Web Token API

These extensions enable advanced features such as secure UUIDs, vector similarity search for future LLM embedding caching, and GraphQL API support.

#### Supabase CLI and Migrations

```bash
# Link to production (xxlpybhprndrdlvbuqoj) or staging (wztqkxsgrwgzeewxcidd)
npx supabase link --project-ref PROJECT_ID

# Create a new migration
npx supabase migration new add_language

# Push migrations to production database
npx supabase db push
```

## Environments:

| Environment | URL | Supabase Project | Deployment |
|-------------|-----|------------------|------------|
| **Production** | [salahscribe.ai](https://salahscribe.ai) | xxlpybhprndrdlvbuqoj | Auto on merge to `main` |
| **Staging** | [staging.salahscribe.ai](https://staging.salahscribe.ai) | wztqkxsgrwgzeewxcidd | Auto on merge to `staging` |
| **Development** | [localhost:8080](https://localhost:8080) | wztqkxsgrwgzeewxcidd | Local dev server |

> **Note**: Edge functions and environment variables require manual deployment after merges using the Supabase deployment commands

### Environment Variables

Environment files are located in `/config` directory and follow Vite naming conventions

#### Frontend Variables (VITE_*)

##### Supabase Configuration
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key
- `SUPABASE_PROJECT_REF` - Supabase project reference ID

##### Environment & Debugging
- `VITE_DEBUG_MODE` - Enable enhanced debugging (true/false)
- `VITE_LOG_LEVEL` - Logging verbosity level (debug/warn/error)

##### Feature Flags
- `VITE_ENABLE_ANALYTICS` - Enable user behavior tracking (true/false)
- `VITE_ENABLE_ERROR_REPORTING` - Enable error monitoring (true/false)

#### Edge Function Variables

##### Text Processing
- `TEXT_LLM_PROVIDER` - Choose `gemini` or `openrouter` (default: `openrouter`)
- `TEXT_API_KEY` - API key for the selected provider (required)
- `TEXT_MODEL` - Model to text (default: `deepseek/deepseek-chat-v3-0324:free`)
- `TEXT_TEMPERATURE` - Generation randomness, 0-2 (default: `0.3`)
- `TEXT_MAX_OUTPUT_TOKENS` - Maximum response length (default: `4096`)

##### Image Processing
- `IMAGE_LLM_PROVIDER` - Provider for image processing (default: `openrouter`)
- `IMAGE_API_KEY` - API key for image processing (required)
- `IMAGE_MODEL` - Model for images (default: `google/gemini-2.0-flash-exp:free`)
- `IMAGE_TEMPERATURE` - Generation randomness, 0-2 (default: `0.3`)
- `IMAGE_MAX_OUTPUT_TOKENS` - Maximum response length (default: `4096`)

##### Audio Processing (TTS)
- `AUDIO_LLM_PROVIDER` - Provider for audio generation (default: `openai`)
- `AUDIO_API_KEY` - API key for the selected provider (required)
- `AUDIO_MODEL` - Model for TTS (e.g., `tts-1`, `tts-1-hd`, `gpt-4o-mini-tts`)
- `AUDIO_VOICE_ARABIC` - Voice for Arabic text (default: `onyx`)
- `AUDIO_VOICE_ENGLISH` - Voice for English text (default: `onyx`)
- `AUDIO_TEMPERATURE` - Generation randomness, 0-2 (default: `0.3`)
- `AUDIO_MAX_OUTPUT_TOKENS` - Maximum response length (default: `4096`)

## Troubleshooting

### LLM Provider Issues
- **Streaming**: Check browser console for SSE errors, verify CORS headers, confirm provider supports streaming.
- **JSON Parsing**: The new `response-parser.ts` utility handles markdown-wrapped JSON responses automatically. All providers now support structured outputs for consistent JSON responses.
- **Markdown-wrapped JSON**: Fixed by the response parser which extracts JSON from ```json...``` blocks before parsing.
- **CORS Errors**: Preflight errors on edge function calls often mean the `OPTIONS` method isn't handled correctly. Ensure the shared `cors.ts` handler includes `Allow-Methods: 'POST, OPTIONS'`.
- **401 Unauthorized**: If services like `ImageOCRService` or `AudioTTSService` fail with a 401 error, ensure the Supabase client is initialized with the `anon_key` as a fallback for unauthenticated users.

### Image Processing Issues
- **Image Upload**: Large images can cause a "Payload Too Large" error or stack overflow when converting to base64 on the client. Implement chunked base64 encoding to handle large files.
- **3MB Image Limit**: Enforce client-side image size limit of 3MB to prevent server overload and timeout errors.
- **Mobile Drag & Drop**: On mobile devices, drag-and-drop may have quirks. Ensure fallback to `<input type="file">` is available.
- **OCR Plain Text Response**: If OCR returns plain text instead of JSON, the LLM providers automatically wrap it in the expected format `{ "extractedText": "..." }`. This is handled transparently by the providers.

### Audio Generation Issues
- **GPT-4o Latency**: Pre-fetch audio on result render; cache blobs in IndexedDB for longer-term storage.
- **Blob URL Management**: Ensure proper cleanup of blob URLs to prevent memory leaks, especially on page navigation.
- **Audio Playback Errors**: Check browser console for MediaError codes. Common issues include unsupported formats or network errors.

### Deployment Issues
- **SSE Disconnects on Vercel**: Send 15-second keep-alive headers in streaming responses; verify in staging environment.
- **Edge Function Timeouts**: Large image processing may hit Vercel's 10-second limit; consider chunked processing.
- **Environment Variables**: After deployment, manually deploy edge functions and environment variables using Supabase CLI commands.
