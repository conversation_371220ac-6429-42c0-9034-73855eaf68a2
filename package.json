{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"lint": "eslint . --config config/eslint.config.js", "lint:css": "stylelint \"src/**/*.css\" --config config/.stylelintrc.json --fix", "dev": "vite --config config/vite.config.ts", "build": "vite build --config config/vite.config.ts", "preview": "vite preview --config config/vite.config.ts", "supabase:dev:env": "node scripts/deploy-env.js --env=dev", "supabase:dev:functions": "npx supabase functions deploy --project-ref wztqkxsgrwgzeewxcidd", "supabase:dev": "npm run supabase:dev:env && npm run supabase:dev:functions", "supabase:staging:env": "node scripts/deploy-env.js --env=staging", "supabase:staging:functions": "npx supabase functions deploy --project-ref wztqkxsgrwgzeewxcidd", "supabase:staging": "npm run supabase:staging:env && npm run supabase:staging:functions", "supabase:prod:env": "node scripts/deploy-env.js", "supabase:prod:functions": "npx supabase functions deploy --project-ref xxlpybhprndrdlvbuqoj", "supabase:prod": "npm run supabase:prod:env && npm run supabase:prod:functions", "deploy:dev": "vercel --local-config config/vercel-dev.json", "deploy:staging": "vercel --local-config config/vercel-staging.json", "deploy:prod": "vercel --local-config config/vercel-prod.json"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.56.2", "caniuse-lite": "^1.0.30001721", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.515.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.6.2", "recharts": "^2.12.7", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.53.2", "@tailwindcss/typography": "^0.5.15", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^16.2.0", "postcss": "^8.4.47", "stylelint": "^16.20.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "supabase": "^2.26.8", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.5"}}