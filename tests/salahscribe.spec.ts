import { test, expect } from '@playwright/test';

test('SalahScribe Arabic to English transliteration flow', async ({ page }) => {
  // 1. Open the app
  await page.goto('http://localhost:8080');

  // 2. Switch to English → Arabic mode
  const directionButton = page.getByRole('button', { name: 'English → Arabic' });
  await directionButton.click();

  // 3. Enter English text in the input box
  const input = page.getByRole('textbox', { name: /Enter .* text here/i });
  await input.fill('apple');

  // 4. Click the "Send text" button
  const sendButton = page.getByRole('button', { name: 'Send text' });
  await expect(sendButton).toBeEnabled();
  await sendButton.click();

  // 5. Wait for the Arabic translation to appear
  await expect(page.getByText('تفاحة', { exact: true })).toBeVisible({ timeout: 5000 });
});