// Test script to verify OpenRouter integration
const testTransliteration = async () => {
  const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://xxlpybhprndrdlvbuqoj.supabase.co';
  const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh4bHB5Ymhwcm5kcmRsdmJ1cW9qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MzMzNjAsImV4cCI6MjA2NDMwOTM2MH0.H5z2IofSsLGxFX2q3sQle3B9NMDuNTAVj5tOTjKTrHM';

  const testText = "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ";
  
  console.log('Testing OpenRouter integration...');
  console.log('Input text:', testText);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/transliterate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({ text: testText })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${error}`);
    }

    const data = await response.json();
    console.log('\n✅ Success! OpenRouter is working correctly.');
    console.log('Transliteration:', data.transliteration);
    console.log('\nYour application is now using OpenRouter with model: deepseek/deepseek-chat-v3-0324:free');
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    console.log('\nPlease check:');
    console.log('1. Your edge function logs at: https://supabase.com/dashboard/project/xxlpybhprndrdlvbuqoj/functions');
    console.log('2. Ensure your OpenRouter API key is valid');
    console.log('3. Verify the model name is correct');
  }
};

// Run the test
testTransliteration();