#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * Test script to verify OCR fix for structured outputs
 * This tests that plain text OCR responses are properly wrapped in JSON
 */

// Mock OCR response that returns plain text
const mockPlainTextOCR = `بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ`;

// Expected wrapped response
const expectedJSON = {
  extractedText: mockPlainTextOCR
};

console.log('🧪 Testing OCR Response Wrapping');
console.log('================================\n');

// Simulate the wrapping logic
function wrapOCRResponse(content: string, responseFormat?: any): string {
  // If we requested structured output but got plain text, wrap it
  if (responseFormat && typeof content === 'string' && !content.trim().startsWith('{')) {
    // For OCR responses, wrap plain text in expected format
    if (responseFormat.json_schema.name === 'ocr_result') {
      return JSON.stringify({ extractedText: content });
    }
  }
  return content;
}

// Test cases
const testCases = [
  {
    name: 'Plain text OCR response with structured format requested',
    input: mockPlainTextOCR,
    responseFormat: {
      type: 'json_schema',
      json_schema: {
        name: 'ocr_result',
        schema: {
          type: 'object',
          properties: {
            extractedText: { type: 'string' }
          },
          required: ['extractedText']
        }
      }
    },
    expected: JSON.stringify(expectedJSON)
  },
  {
    name: 'Already JSON formatted response',
    input: '{"extractedText": "test"}',
    responseFormat: {
      type: 'json_schema',
      json_schema: {
        name: 'ocr_result',
        schema: {}
      }
    },
    expected: '{"extractedText": "test"}'
  },
  {
    name: 'Plain text without structured format',
    input: 'plain text',
    responseFormat: null,
    expected: 'plain text'
  },
  {
    name: 'Non-OCR structured format',
    input: 'some text',
    responseFormat: {
      type: 'json_schema',
      json_schema: {
        name: 'translation_result',
        schema: {}
      }
    },
    expected: 'some text'
  }
];

let passed = 0;
let failed = 0;

for (const testCase of testCases) {
  console.log(`Test: ${testCase.name}`);
  const result = wrapOCRResponse(testCase.input, testCase.responseFormat);
  
  if (result === testCase.expected) {
    console.log('✅ PASSED');
    console.log(`   Input: "${testCase.input.substring(0, 50)}${testCase.input.length > 50 ? '...' : ''}"`);
    console.log(`   Output: ${result.substring(0, 100)}${result.length > 100 ? '...' : ''}`);
    passed++;
  } else {
    console.log('❌ FAILED');
    console.log(`   Input: "${testCase.input}"`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Got: ${result}`);
    failed++;
  }
  console.log();
}

console.log('================================');
console.log(`📊 Results: ${passed} passed, ${failed} failed`);

if (failed > 0) {
  console.error('\n❌ Some tests failed!');
  Deno.exit(1);
} else {
  console.log('\n✅ All tests passed! The OCR fix is working correctly.');
  console.log('\nThe fix ensures that when image models return plain text instead of JSON,');
  console.log('the response is automatically wrapped in the expected format.');
}