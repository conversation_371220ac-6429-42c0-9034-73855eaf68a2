#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * Test script to verify structured outputs are working correctly
 * This script tests each endpoint to ensure they return pure JSON without markdown wrapping
 */

const BASE_URL = 'http://localhost:54321/functions/v1';
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');

if (!OPENAI_API_KEY) {
  console.error('❌ OPENAI_API_KEY environment variable is required');
  Deno.exit(1);
}

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${OPENAI_API_KEY}`
};

interface TestCase {
  name: string;
  endpoint: string;
  body: Record<string, any>;
  validateResponse: (response: any) => void;
}

const testCases: TestCase[] = [
  {
    name: 'Translation (Arabic to English)',
    endpoint: '/translate',
    body: {
      text: 'السلام عليكم',
      sourceLang: 'ar',
      targetLang: 'en'
    },
    validateResponse: (response) => {
      if (!response.translation || typeof response.translation !== 'string') {
        throw new Error('Invalid translation response');
      }
    }
  },
  {
    name: 'Translation (English to Arabic)',
    endpoint: '/translate',
    body: {
      text: 'Peace be upon you',
      sourceLang: 'en',
      targetLang: 'ar'
    },
    validateResponse: (response) => {
      if (!response.translation || typeof response.translation !== 'string') {
        throw new Error('Invalid translation response');
      }
    }
  },
  {
    name: 'Transliteration (Arabic to Latin)',
    endpoint: '/transliterate',
    body: {
      text: 'بسم الله الرحمن الرحيم',
      sourceLang: 'ar'
    },
    validateResponse: (response) => {
      if (!response.transliteration || typeof response.transliteration !== 'string') {
        throw new Error('Invalid transliteration response');
      }
    }
  },
  {
    name: 'Transliteration (English to Arabic)',
    endpoint: '/transliterate',
    body: {
      text: 'bismillah',
      sourceLang: 'en'
    },
    validateResponse: (response) => {
      if (!response.transliteration || typeof response.transliteration !== 'string') {
        throw new Error('Invalid transliteration response');
      }
    }
  },
  // Note: Image endpoint testing requires actual image data
  // Uncomment and provide a valid image to test
  /*
  {
    name: 'Image Transliteration (Arabic image)',
    endpoint: '/transliterate-image',
    body: new FormData(), // Would need to append image file
    validateResponse: (response) => {
      if (!response.extractedText || typeof response.extractedText !== 'string') {
        throw new Error('Invalid extractedText in response');
      }
      if (!response.transliteration || typeof response.transliteration !== 'string') {
        throw new Error('Invalid transliteration in response');
      }
      if (!response.translation || typeof response.translation !== 'string') {
        throw new Error('Invalid translation in response');
      }
    }
  }
  */
];

async function runTest(testCase: TestCase): Promise<void> {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log(`   Endpoint: ${testCase.endpoint}`);
  console.log(`   Request:`, JSON.stringify(testCase.body, null, 2));
  
  try {
    const response = await fetch(`${BASE_URL}${testCase.endpoint}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(testCase.body)
    });
    
    const responseText = await response.text();
    console.log(`   Raw Response: ${responseText.substring(0, 100)}...`);
    
    // Check if response is wrapped in markdown
    if (responseText.includes('```json') || responseText.includes('```')) {
      console.error('   ❌ Response contains markdown code blocks!');
      throw new Error('Response is wrapped in markdown, structured outputs not working');
    }
    
    // Try to parse as JSON
    let jsonResponse;
    try {
      jsonResponse = JSON.parse(responseText);
    } catch (e) {
      console.error('   ❌ Failed to parse response as JSON');
      throw new Error('Response is not valid JSON');
    }
    
    // Validate response structure
    testCase.validateResponse(jsonResponse);
    
    console.log('   ✅ Test passed! Response:', JSON.stringify(jsonResponse, null, 2));
  } catch (error) {
    console.error(`   ❌ Test failed: ${error.message}`);
    throw error;
  }
}

async function runAllTests(): Promise<void> {
  console.log('🚀 Starting Structured Outputs Test Suite');
  console.log('=====================================\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    try {
      await runTest(testCase);
      passed++;
    } catch (error) {
      failed++;
    }
  }
  
  console.log('\n=====================================');
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed > 0) {
    console.error('\n❌ Some tests failed. Structured outputs may not be working correctly.');
    Deno.exit(1);
  } else {
    console.log('\n✅ All tests passed! Structured outputs are working correctly.');
    console.log('\n📝 Note: Image endpoint testing requires actual image data.');
    console.log('   To test the image endpoint, uncomment the test case and provide a valid image file.');
  }
}

// Run tests
await runAllTests();